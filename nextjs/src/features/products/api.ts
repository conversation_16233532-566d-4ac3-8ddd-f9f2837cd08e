import { createSPASassClient } from '@/lib/supabase/client';
import { createServerClient } from '@/lib/supabase/server';
import {
  Product,
  PaginationParams,
  ProductFilterParams,
  PaginatedResponse,
  ProductsApiResponse,
  ProductApiResponse
} from './types';

// Server-side API functions
export async function getProducts(
  paginationParams: PaginationParams = { page: 1, limit: 10 },
  filterParams: ProductFilterParams = {}
): Promise<PaginatedResponse<Product>> {
  try {
    const supabase = await createServerClient();
    let query = supabase
      .from('products')
      .select('*, category:categories(*), store:stores(*)', { count: 'exact' });

    // Apply filters
    if (filterParams.categoryId) {
      query = query.eq('category_id', filterParams.categoryId);
    }
    if (filterParams.storeId) {
      query = query.eq('store_id', filterParams.storeId);
    }
    if (filterParams.featured !== undefined) {
      query = query.eq('featured', filterParams.featured);
    }
    if (filterParams.inStock !== undefined) {
      query = query.eq('in_stock', filterParams.inStock);
    }
    if (filterParams.search) {
      query = query.ilike('name', `%${filterParams.search}%`);
    }

    // Apply pagination
    const { page, limit } = paginationParams;
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Apply sorting
    if (filterParams.sortBy) {
      const ascending = filterParams.sortOrder === 'asc';
      query = query.order(filterParams.sortBy, { ascending });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const { data, error, count } = await query;
    if (error) throw error;

    return {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };

    // if (error) throw error;
    // return { data: data || [], meta: { ... } };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      data: [],
      meta: {
        total: 0,
        page: paginationParams.page,
        limit: paginationParams.limit,
        totalPages: 0
      }
    };
  }
}

export async function getProductBySlug(slug: string): Promise<Product | null> {
  try {
    // For now, use mock data
    return await MockDataService.getProductBySlug(slug);

    // When ready to switch to real data:
    // const supabase = await createServerClient();
    // const { data, error } = await supabase
    //   .from('products')
    //   .select('*')
    //   .eq('slug', slug)
    //   .single();

    // if (error) throw error;
    // return data;
  } catch (error) {
    console.error(`Error fetching product with slug ${slug}:`, error);
    return null;
  }
}

export async function getFeaturedProducts(limit = 8): Promise<Product[]> {
  try {
    // For now, use mock data
    const response = await MockDataService.getProducts(
      { page: 1, limit },
      { featured: true }
    );
    return response.data;

    // When ready to switch to real data:
    // const supabase = await createServerClient();
    // const { data, error } = await supabase
    //   .from('products')
    //   .select('*')
    //   .eq('featured', true)
    //   .limit(limit);

    // if (error) throw error;
    // return data || [];
  } catch (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }
}

// Client-side API functions
export async function getProductsClient(
  paginationParams: PaginationParams = { page: 1, limit: 10 },
  filterParams: ProductFilterParams = {}
): Promise<ProductsApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    let query = supabase
      .from('products')
      .select('*, category:categories(*), store:stores(*)', { count: 'exact' });

    // Apply filters
    if (filterParams.categoryId) {
      query = query.eq('category_id', filterParams.categoryId);
    }
    if (filterParams.storeId) {
      query = query.eq('store_id', filterParams.storeId);
    }
    if (filterParams.featured !== undefined) {
      query = query.eq('featured', filterParams.featured);
    }
    if (filterParams.inStock !== undefined) {
      query = query.eq('in_stock', filterParams.inStock);
    }
    if (filterParams.search) {
      query = query.ilike('name', `%${filterParams.search}%`);
    }

    // Apply pagination
    const { page, limit } = paginationParams;
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Apply sorting
    if (filterParams.sortBy) {
      const ascending = filterParams.sortOrder === 'asc';
      query = query.order(filterParams.sortBy, { ascending });
    } else {
      query = query.order('created_at', { ascending: false });
    }

    const { data, error, count } = await query;
    if (error) throw error;

    const response = {
      data: data || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };

    return { products: response };

    // if (error) throw error;
    // return { products: data };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      products: {
        data: [],
        meta: {
          total: 0,
          page: paginationParams.page,
          limit: paginationParams.limit,
          totalPages: 0
        }
      },
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
