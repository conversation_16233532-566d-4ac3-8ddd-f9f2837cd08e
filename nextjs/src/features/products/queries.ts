'use client';

import { useQuery } from '@tanstack/react-query';
import { 
  getProductsClient, 
  getProductBySlugClient,
  getFeaturedProductsClient,
  getDeliveryServicesClient,
  getCourierServicesClient
} from './api-client';
import type { 
  PaginationParams, 
  ProductFilterParams, 
  Product 
} from './types';

// Query keys
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (params: PaginationParams, filters: ProductFilterParams) => 
    [...productKeys.lists(), params, filters] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (slug: string) => [...productKeys.details(), slug] as const,
  featured: () => [...productKeys.all, 'featured'] as const,
  trending: () => [...productKeys.all, 'trending'] as const,
};

export const shippingKeys = {
  all: ['shipping'] as const,
  deliveryServices: () => [...shippingKeys.all, 'delivery-services'] as const,
  courierServices: () => [...shippingKeys.all, 'courier-services'] as const,
};

// Query hooks
export function useProducts(
  paginationParams: PaginationParams = { page: 1, limit: 10 },
  filterParams: ProductFilterParams = {}
) {
  return useQuery({
    queryKey: productKeys.list(paginationParams, filterParams),
    queryFn: () => getProductsClient(paginationParams, filterParams),
  });
}

export function useProduct(slug: string) {
  return useQuery({
    queryKey: productKeys.detail(slug),
    queryFn: () => getProductBySlugClient(slug),
    enabled: !!slug,
  });
}

export function useFeaturedProducts(limit = 8) {
  return useQuery({
    queryKey: [...productKeys.featured(), limit],
    queryFn: () => getFeaturedProductsClient(limit),
  });
}

export function useTrendingProducts(limit = 4) {
  return useQuery({
    queryKey: [...productKeys.trending(), limit],
    queryFn: () => getProductsClient(
      { page: 1, limit },
      { trending: true }
    ),
  });
}

// Shipping service hooks
export function useDeliveryServices() {
  return useQuery({
    queryKey: shippingKeys.deliveryServices(),
    queryFn: () => getDeliveryServicesClient(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCourierServices() {
  return useQuery({
    queryKey: shippingKeys.courierServices(),
    queryFn: () => getCourierServicesClient(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
