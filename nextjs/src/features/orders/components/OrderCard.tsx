'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { UserOrder } from '../api';
import { formatCurrency } from '@/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { 
  Calendar, 
  Package, 
  Eye, 
  Clock, 
  CheckCircle, 
  Truck, 
  XCircle,
  AlertCircle,
  MapPin
} from 'lucide-react';


interface OrderCardProps {
  order: UserOrder;
}

export function OrderCard({ order }: OrderCardProps) {
  const getStatusIcon = () => {
    switch (order.status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'processing':
        return <Package className="h-4 w-4" />;
      case 'shipped':
        return <Truck className="h-4 w-4" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      case 'refunded':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = () => {
    switch (order.status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'shipped':
        return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'refunded':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold text-lg">
              Order #{order.id.substring(0, 8)}
            </h3>
            <div className="flex items-center text-sm text-gray-500 mt-1">
              <Calendar className="h-4 w-4 mr-1" />
              {new Date(order.created_at).toLocaleDateString()}
            </div>
          </div>
          <Badge 
            variant="outline" 
            className={`${getStatusColor()} flex items-center gap-1`}
          >
            {getStatusIcon()}
            {formatStatus(order.status)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Total Amount</span>
          <span className="font-semibold text-lg">
            {formatCurrency(order.total, order.currency)}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Items</span>
          <span className="text-sm font-medium">
            {order.items_count} {order.items_count === 1 ? 'item' : 'items'}
          </span>
        </div>

        {/* Delivery Method */}
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Delivery Method</span>
          <div className="flex items-center gap-1 text-sm font-medium">
            {order.delivery_method === 'pickup' ? (
              <>
                <MapPin className="h-3 w-3 text-green-600" />
                <span>Store Pickup</span>
              </>
            ) : order.delivery_method === 'delivery' ? (
              <>
                <Truck className="h-3 w-3 text-blue-600" />
                <span>
                  {order.delivery_service?.name 
                    ? `${order.delivery_service.name}`
                    : 'Home Delivery'
                  }
                </span>
              </>
            ) : (
              <span className="text-gray-400">Not specified</span>
            )}
          </div>
        </div>

        {order.shipping_address && (
          <div className="text-sm text-gray-600">
            <span className="font-medium">Shipping to:</span>
            <div className="mt-1">
              {order.shipping_name && <div>{order.shipping_name}</div>}
              <div>{order.shipping_address}</div>
              {order.shipping_city && (
                <div>
                  {[order.shipping_city, order.shipping_state, order.shipping_country]
                    .filter(Boolean)
                    .join(', ')}
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-3 border-t">
        <Link href={`/my-orders/${order.id}`} className="w-full">
          <Button variant="outline" className="w-full">
            <Eye className="h-4 w-4 mr-2" />
            View Details
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
