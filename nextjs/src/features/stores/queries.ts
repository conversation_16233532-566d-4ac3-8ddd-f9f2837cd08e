'use client';

import { useQuery } from '@tanstack/react-query';
import {
  getStoresClient,
  getStoreBySlugClient,
  getFeaturedStoresClient
} from './api-client';
import type { StoreFilterParams } from './types';

// Query keys
export const storeKeys = {
  all: ['stores'] as const,
  lists: () => [...storeKeys.all, 'list'] as const,
  list: (filters: StoreFilterParams) => [...storeKeys.lists(), filters] as const,
  details: () => [...storeKeys.all, 'detail'] as const,
  detail: (slug: string) => [...storeKeys.details(), slug] as const,
  featured: () => [...storeKeys.all, 'featured'] as const,
};

// Query hooks
export function useStores(filters: StoreFilterParams = {}) {
  return useQuery({
    queryKey: storeKeys.list(filters),
    queryFn: () => getStoresClient(filters),
  });
}

export function useStore(slug: string) {
  return useQuery({
    queryKey: storeKeys.detail(slug),
    queryFn: () => getStoreBySlugClient(slug),
    enabled: !!slug,
  });
}

export function useFeaturedStores(limit = 4) {
  return useQuery({
    queryKey: [...storeKeys.featured(), limit],
    queryFn: async () => {
      // Use the same function that works for the stores page, but don't filter by featured
      const result = await getStoresClient({
        // Don't filter by featured status to show all stores
        sortBy: 'name',
        sortOrder: 'asc'
      });

      // Limit the number of stores returned
      if (result.stores && result.stores.length > limit) {
        result.stores = result.stores.slice(0, limit);
      }

      return result;
    },
  });
}
