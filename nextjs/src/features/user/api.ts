import { createSPASassClient } from '@/lib/supabase/client';
import {
  UserProfile,
  UserSettings,
  UpdateProfileParams,
  UpdateSettingsParams,
  ChangePasswordParams,
  UserError
} from './types';

export async function getUserProfile(): Promise<{ profile: UserProfile | null, error: UserError | null }> {
  try {
    const client = await createSPASassClient();
    const { data, error } = await client.getUserProfile();

    if (error) {
      return { profile: null, error: { message: error.message } };
    }

    return { profile: data, error: null };
  } catch (error) {
    return {
      profile: null,
      error: {
        message: error instanceof Error ? error.message : 'An unknown error occurred while fetching user profile'
      }
    };
  }
}

export async function updateUserProfile(params: UpdateProfileParams): Promise<{ profile: UserProfile | null, error: UserError | null }> {
  try {
    const client = await createSPASassClient();
    const { data, error } = await client.updateUserProfile(params);

    if (error) {
      return { profile: null, error: { message: error.message } };
    }

    return { profile: data, error: null };
  } catch (error) {
    return {
      profile: null,
      error: {
        message: error instanceof Error ? error.message : 'An unknown error occurred while updating user profile'
      }
    };
  }
}

export async function changePassword(params: ChangePasswordParams): Promise<{ success: boolean, error: UserError | null }> {
  try {
    if (params.new_password !== params.confirm_password) {
      return { success: false, error: { message: 'New passwords do not match', field: 'confirm_password' } };
    }

    const client = await createSPASassClient();
    const { error } = await client.changePassword(params.current_password, params.new_password);

    if (error) {
      return { success: false, error: { message: error.message } };
    }

    return { success: true, error: null };
  } catch (error) {
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : 'An unknown error occurred while changing password'
      }
    };
  }
}

export async function uploadAvatar(file: File): Promise<{ url: string | null, error: UserError | null }> {
  try {
    const client = await createSPASassClient();

    // Generate a unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    const filePath = `avatars/${fileName}`;

    // Upload the file
    const { error: uploadError } = await client.storage.from('profiles').upload(filePath, file, {
      cacheControl: '3600',
      upsert: false
    });

    if (uploadError) {
      return { url: null, error: { message: uploadError.message } };
    }

    // Get the public URL
    const { data } = await client.storage.from('profiles').getPublicUrl(filePath);

    // Update the user profile with the new avatar URL
    const { error: updateError } = await client.updateUserProfile({
      avatar_url: data.publicUrl
    });

    if (updateError) {
      return { url: null, error: { message: updateError.message } };
    }

    return { url: data.publicUrl, error: null };
  } catch (error) {
    return {
      url: null,
      error: {
        message: error instanceof Error ? error.message : 'An unknown error occurred while uploading avatar'
      }
    };
  }
}
