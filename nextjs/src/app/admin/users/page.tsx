"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';

import { AdminUser, UserListParams, UserRole } from '@/features/admin/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Edit, Eye } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

export default function UsersPage() {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [params, setParams] = useState<UserListParams>({
    page: 1,
    per_page: 10,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        // Build query string from params
        const queryParams = new URLSearchParams();
        if (params.page) queryParams.set('page', params.page.toString());
        if (params.per_page) queryParams.set('per_page', params.per_page.toString());
        if (params.search) queryParams.set('search', params.search);
        if (params.role) queryParams.set('role', params.role);
        if (params.sort_by) queryParams.set('sort_by', params.sort_by);
        if (params.sort_order) queryParams.set('sort_order', params.sort_order);

        // Fetch users from API
        const response = await fetch(`/api/admin/users?${queryParams.toString()}`);
        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }

        const data = await response.json();
        setUsers(data.users);
        setTotalCount(data.count);
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [params]);

  const handlePageChange = (page: number) => {
    setParams({ ...params, page });
  };

  const handleSearch = (search: string) => {
    setParams({ ...params, search, page: 1 });
  };

  const handleSortChange = (key: string, order: 'asc' | 'desc') => {
    setParams({ ...params, sort_by: key, sort_order: order, page: 1 });
  };

  const handleRoleFilter = (role: UserRole | 'all') => {
    if (role === 'all') {
      const { role, ...rest } = params;
      setParams({ ...rest, page: 1 });
    } else {
      setParams({ ...params, role, page: 1 });
    }
  };

  const columns = [
    {
      key: 'email',
      header: 'Email',
      cell: (user: AdminUser) => (
        <div className="font-medium">{user.email}</div>
      ),
      sortable: true,
    },
    {
      key: 'name',
      header: 'Name',
      cell: (user: AdminUser) => (
        <div>
          {user.first_name || user.last_name
            ? `${user.first_name || ''} ${user.last_name || ''}`
            : '-'}
        </div>
      ),
    },
    {
      key: 'role',
      header: 'Role',
      cell: (user: AdminUser) => <StatusBadge status={user.role} />,
      sortable: true,
    },
    {
      key: 'phone',
      header: 'Phone',
      cell: (user: AdminUser) => <div>{user.phone || '-'}</div>,
    },
    {
      key: 'created_at',
      header: 'Joined',
      cell: (user: AdminUser) => (
        <div>{new Date(user.created_at).toLocaleDateString()}</div>
      ),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (user: AdminUser) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/users/${user.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/users/${user.id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Users"
        description="Manage your users"
      />

      <div className="flex items-center gap-4 mb-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Role:</span>
          <Select
            value={params.role || 'all'}
            onValueChange={(value) => handleRoleFilter(value as UserRole | 'all')}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="All Roles" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="user">User</SelectItem>
              <SelectItem value="store_owner">Store Owner</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={users}
        totalCount={totalCount}
        pageSize={params.per_page || 10}
        currentPage={params.page || 1}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search users..."
        onSortChange={handleSortChange}
        sortKey={params.sort_by}
        sortOrder={params.sort_order}
        isLoading={loading}
        getRowKey={(user) => user.id}
      />
    </div>
  );
}
