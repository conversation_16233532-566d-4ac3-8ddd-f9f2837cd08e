"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ExternalDeliveryService } from '@/features/admin/types';
import DataTable from '@/components/admin/DataTable'; // Changed to default import
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Truck,
  Phone,
  Mail,
  Globe,
  MapPin,
  Clock,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';

export default function DeliveryServicesPage() {
  const [deliveryServices, setDeliveryServices] = useState<ExternalDeliveryService[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const perPage = 10;

  useEffect(() => {
    fetchDeliveryServices();
  }, [currentPage, searchTerm]);

  const fetchDeliveryServices = async () => {
    setLoading(true);
    setError(null); // Clear previous errors
    console.log('[AdminDSPage] Fetching delivery services. Params:', { currentPage, perPage, searchTerm });
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: perPage.toString(),
        ...(searchTerm && { search: searchTerm }),
      });
      const apiUrl = `/api/admin/delivery-services?${params}`;
      console.log('[AdminDSPage] Calling API:', apiUrl);

      const response = await fetch(apiUrl);
      console.log('[AdminDSPage] API response status:', response.status);

      const data = await response.json();
      console.log('[AdminDSPage] API response data:', data);

      if (!response.ok || data.error) {
        const errorMessage = data.error || `Failed with status ${response.status}`;
        console.error('[AdminDSPage] API error:', errorMessage);
        throw new Error(errorMessage);
      }

      setDeliveryServices(data.services || []);
      setTotalCount(data.total || 0);
      console.log('[AdminDSPage] Successfully updated state with services:', data.services, 'and total count:', data.total);
    } catch (error: any) {
      console.error('[AdminDSPage] Error in fetchDeliveryServices:', error.message, error);
      setError(error.message || 'Failed to load delivery services');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this delivery service?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/delivery-services/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete delivery service');
      }

      await fetchDeliveryServices();
    } catch (error) {
      console.error('Error deleting delivery service:', error);
      setError('Failed to delete delivery service');
    }
  };

  const formatCurrency = (amount: number) => {
    return `GMD ${amount.toFixed(2)}`;
  };

  const columns = [
    {
      key: 'name',
      header: 'Service Name',
      cell: (service: ExternalDeliveryService) => (
        <div className="flex items-center gap-3">
          {service.logo ? (
            <img
              src={service.logo}
              alt={service.name}
              className="w-8 h-8 rounded-full object-cover"
            />
          ) : (
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <Truck className="h-4 w-4 text-primary-600" />
            </div>
          )}
          <div>
            <div className="font-medium">{service.name}</div>
            {service.description && (
              <div className="text-sm text-gray-500 truncate max-w-xs">
                {service.description}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'contact',
      header: 'Contact Info',
      cell: (service: ExternalDeliveryService) => (
        <div className="space-y-1">
          {service.contact_phone && (
            <div className="flex items-center gap-1 text-sm">
              <Phone className="h-3 w-3 text-gray-400" />
              <span>{service.contact_phone}</span>
            </div>
          )}
          {service.contact_email && (
            <div className="flex items-center gap-1 text-sm">
              <Mail className="h-3 w-3 text-gray-400" />
              <span>{service.contact_email}</span>
            </div>
          )}
          {service.website && (
            <div className="flex items-center gap-1 text-sm">
              <Globe className="h-3 w-3 text-gray-400" />
              <span className="truncate max-w-xs">{service.website}</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'pricing',
      header: 'Pricing',
      cell: (service: ExternalDeliveryService) => (
        <div className="space-y-1">
          <div className="flex items-center gap-1 text-sm">
            <DollarSign className="h-3 w-3 text-gray-400" />
            <span>Base: {formatCurrency(service.base_fee)}</span>
          </div>
          {service.per_km_fee && service.per_km_fee > 0 && (
            <div className="text-sm text-gray-600">
              + {formatCurrency(service.per_km_fee)}/km
            </div>
          )}
          {service.minimum_order_amount && service.minimum_order_amount > 0 && (
            <div className="text-xs text-gray-500">
              Min: {formatCurrency(service.minimum_order_amount)}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'coverage',
      header: 'Coverage & Time',
      cell: (service: ExternalDeliveryService) => (
        <div className="space-y-1">
          <div className="flex items-center gap-1 text-sm">
            <MapPin className="h-3 w-3 text-gray-400" />
            <span>{service.maximum_delivery_radius_km || 'N/A'} km radius</span>
          </div>
          <div className="flex items-center gap-1 text-sm">
            <Clock className="h-3 w-3 text-gray-400" />
            <span>{service.estimated_delivery_time || 'N/A'}</span>
          </div>
          {service.coverage_areas && service.coverage_areas.length > 0 && (
            <div className="text-xs text-gray-500">
              {service.coverage_areas.slice(0, 2).join(', ')}
              {service.coverage_areas.length > 2 && ` +${service.coverage_areas.length - 2} more`}
            </div>
          )}
        </div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      cell: (service: ExternalDeliveryService) => (
        <Badge variant={service.is_active ? 'default' : 'secondary'}>
          {service.is_active ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (service: ExternalDeliveryService) => (
        <div className="flex space-x-2">
          <Link href={`/admin/delivery-services/${service.id}`}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/delivery-services/${service.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDelete(service.id)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title="External Delivery Services"
        description="Manage third-party delivery service providers"
        // icon={Truck} // Removed icon prop as it's not supported
      />

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search delivery services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Link href="/admin/delivery-services/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Delivery Service
          </Button>
        </Link>
      </div>

      {/* Services Table */}
      <div className="bg-white rounded-lg shadow-sm border">
        <DataTable
          columns={columns}
          data={deliveryServices}
          totalCount={totalCount}
          pageSize={perPage}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onSearch={(search: string) => setSearchTerm(search)} // Added type for search
          searchPlaceholder="Search delivery services..."
          isLoading={loading}
          getRowKey={(service) => service.id}
        />
      </div>
    </div>
  );
}
