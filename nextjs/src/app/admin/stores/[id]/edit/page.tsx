"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminHeader, ImageUpload } from '@/components/admin';
import { getStore, updateStore } from '@/features/admin/api';
import { AdminStore, StoreStatus, UpdateStoreParams } from '@/features/admin/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertCircle, Star } from 'lucide-react';
import Image from 'next/image';

export default function EditStorePage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [store, setStore] = useState<AdminStore | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [storeId, setStoreId] = useState<string | null>(null);
  const [formData, setFormData] = useState<UpdateStoreParams>({
    name: '',
    description: '',
    contact_email: '',
    contact_phone: '',
    address: '',
    status: 'active' as StoreStatus,
    featured: false,
    logo: '',
    cover_image: '',
  });

  // Unwrap params using React.use()
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setStoreId(resolvedParams.id);
    };
    unwrapParams();
  }, [params]);

  useEffect(() => {
    if (!storeId) return;

    const fetchStore = async () => {
      setLoading(true);
      try {
        const storeData = await getStore(storeId);
        if (storeData) {
          setStore(storeData);
          setFormData({
            name: storeData.name,
            description: storeData.description || '',
            contact_email: storeData.contact_email || '',
            address: storeData.address || '',
            contact_phone: storeData.contact_phone || '',
            status: storeData.status,
            featured: storeData.featured,
            logo: storeData.logo || '',
            cover_image: storeData.cover_image || '',
          });
        }
      } catch (error) {
        console.error('Error fetching store:', error);
        setError('Failed to load store data');
      } finally {
        setLoading(false);
      }
    };

    fetchStore();
  }, [storeId]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleStatusChange = (status: StoreStatus) => {
    setFormData((prev) => ({ ...prev, status }));
  };

  const handleFeaturedChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, featured: checked }));
  };

  const handleLogoUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, logo: url }));
  };

  const handleCoverImageUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, cover_image: url }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      if (!storeId) return;
      const updatedStore = await updateStore(storeId, formData);
      if (updatedStore) {
        router.push('/admin/stores');
        router.refresh();
      } else {
        setError('Failed to update store');
      }
    } catch (error) {
      console.error('Error updating store:', error);
      setError('An error occurred while updating the store');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!store) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Store Not Found"
          description="The requested store could not be found"
          backHref="/admin/stores"
        />
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            The store you are trying to edit does not exist or you don't have permission to view it.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Edit Store"
        description="Update store information and status"
        backHref="/admin/stores"
      />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Store Information</CardTitle>
                <CardDescription>
                  Edit the store's basic information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Store Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Address</Label>
                  <Textarea
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    rows={4}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contact_email">Contact Email</Label>
                    <Input
                      id="contact_email"
                      name="contact_email"
                      type="email"
                      value={formData.contact_email}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contact_phone">Contact Phone</Label>
                    <Input
                      id="contact_phone"
                      name="contact_phone"
                      value={formData.contact_phone}
                      onChange={handleChange}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Store Images</CardTitle>
                <CardDescription>
                  Upload logo and cover image for the store
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <ImageUpload
                  label="Store Logo"
                  description="Upload a logo for your store (square format recommended)"
                  currentImageUrl={formData.logo}
                  onImageUploaded={handleLogoUploaded}
                  bucketName="store-images"
                  folderPath="logos"
                  aspectRatio="logo"
                />

                <ImageUpload
                  label="Cover Image"
                  description="Upload a cover image for your store (16:9 format recommended)"
                  currentImageUrl={formData.cover_image}
                  onImageUploaded={handleCoverImageUploaded}
                  bucketName="store-images"
                  folderPath="covers"
                  aspectRatio="cover"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Store Status</CardTitle>
                <CardDescription>
                  Manage the store's status and visibility
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleStatusChange(value as StoreStatus)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="disabled">Disabled</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between space-x-2 pt-4">
                  <div className="space-y-0.5">
                    <Label htmlFor="featured">Featured Store</Label>
                    <div className="text-sm text-gray-500">
                      Show this store on the homepage
                    </div>
                  </div>
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={handleFeaturedChange}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Store Owner</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm">
                  <p className="font-medium">{store.owner_email}</p>
                  <p className="text-gray-500 mt-1">Owner ID: {store.owner_id}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/stores')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Changes
          </Button>
        </div>
      </form>
    </div>
  );
}
