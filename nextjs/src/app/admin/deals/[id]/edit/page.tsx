'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Save, Calendar, DollarSign } from 'lucide-react';
import Link from 'next/link';
import { AdminHeader } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { getDeal, updateDeal } from '@/features/admin/api';
import { AdminDeal, UpdateDealParams, DealType } from '@/features/admin/types';
import { useToast } from '@/lib/hooks/use-toast';

export default function EditDealPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { toast } = useToast();
  const [deal, setDeal] = useState<AdminDeal | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [dealId, setDealId] = useState<string | null>(null);

  const [formData, setFormData] = useState<UpdateDealParams>({
    title: '',
    description: '',
    original_price: 0,
    deal_price: 0,
    deal_type: 'regular',
    start_date: '',
    end_date: '',
    is_active: true,
    featured: false,
    max_quantity: undefined
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Unwrap params using React.use()
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setDealId(resolvedParams.id);
    };
    unwrapParams();
  }, [params]);

  useEffect(() => {
    if (!dealId) return;

    const fetchDeal = async () => {
      try {
        setLoading(true);
        const fetchedDeal = await getDeal(dealId);
        if (fetchedDeal) {
          setDeal(fetchedDeal);
          setFormData({
            title: fetchedDeal.title,
            description: fetchedDeal.description || '',
            original_price: fetchedDeal.original_price,
            deal_price: fetchedDeal.deal_price,
            deal_type: fetchedDeal.deal_type,
            start_date: fetchedDeal.start_date.split('T')[0],
            end_date: fetchedDeal.end_date.split('T')[0],
            is_active: fetchedDeal.is_active,
            featured: fetchedDeal.featured,
            max_quantity: fetchedDeal.max_quantity
          });
        }
      } catch (error) {
        console.error('Error fetching deal:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch deal details',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDeal();
  }, [dealId, toast]);

  const handleInputChange = (field: keyof UpdateDealParams, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title?.trim()) newErrors.title = 'Title is required';
    if (!formData.original_price || formData.original_price <= 0) {
      newErrors.original_price = 'Original price must be greater than 0';
    }
    if (!formData.deal_price || formData.deal_price <= 0) {
      newErrors.deal_price = 'Deal price must be greater than 0';
    }
    if (formData.deal_price && formData.original_price && formData.deal_price >= formData.original_price) {
      newErrors.deal_price = 'Deal price must be less than original price';
    }
    if (!formData.start_date) newErrors.start_date = 'Start date is required';
    if (!formData.end_date) newErrors.end_date = 'End date is required';
    if (formData.start_date && formData.end_date && new Date(formData.end_date) <= new Date(formData.start_date)) {
      newErrors.end_date = 'End date must be after start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setSaving(true);
      const updatedDeal = await updateDeal(dealId, formData);

      if (updatedDeal) {
        toast({
          title: 'Success',
          description: 'Deal updated successfully'
        });
        router.push(`/admin/deals/${dealId}`);
      } else {
        throw new Error('Failed to update deal');
      }
    } catch (error) {
      console.error('Error updating deal:', error);
      toast({
        title: 'Error',
        description: 'Failed to update deal',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  const discountPercentage = formData.original_price && formData.deal_price && formData.original_price > 0 && formData.deal_price > 0
    ? Math.round(((formData.original_price - formData.deal_price) / formData.original_price) * 100)
    : 0;

  if (loading) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Edit Deal"
          description="Loading deal information..."
          breadcrumbs={[
            { label: 'Deals', href: '/admin/deals' },
            { label: 'Edit Deal' }
          ]}
        />
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!deal) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Deal Not Found"
          description="The requested deal could not be found"
          breadcrumbs={[
            { label: 'Deals', href: '/admin/deals' },
            { label: 'Edit Deal' }
          ]}
        />
        <div className="bg-white p-6 rounded-lg shadow-sm border text-center">
          <p className="text-gray-500 mb-4">Deal not found</p>
          <Link href="/admin/deals">
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Deals
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title={`Edit: ${deal.title}`}
        description="Update deal information and settings"
        breadcrumbs={[
          { label: 'Deals', href: '/admin/deals' },
          { label: deal.title, href: `/admin/deals/${deal.id}` },
          { label: 'Edit' }
        ]}
      />

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-medium mb-4">Deal Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Product Information (Read-only) */}
            <div className="md:col-span-2 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium mb-2">Product Information</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <div><strong>Product:</strong> {deal.product_name}</div>
                <div><strong>Store:</strong> {deal.store_name}</div>
                <div><strong>Currency:</strong> {deal.currency}</div>
              </div>
            </div>

            {/* Title */}
            <div className="md:col-span-2">
              <Label htmlFor="title">Deal Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Enter deal title"
                className="mt-1"
              />
              {errors.title && (
                <p className="text-red-500 text-sm mt-1">{errors.title}</p>
              )}
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <Label htmlFor="description">Description</Label>
              <textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter deal description"
                rows={3}
                className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            {/* Pricing */}
            <div>
              <Label htmlFor="original_price">Original Price *</Label>
              <div className="mt-1 relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="original_price"
                  type="number"
                  step="0.01"
                  value={formData.original_price}
                  onChange={(e) => handleInputChange('original_price', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className="pl-10"
                />
              </div>
              {errors.original_price && (
                <p className="text-red-500 text-sm mt-1">{errors.original_price}</p>
              )}
            </div>

            <div>
              <Label htmlFor="deal_price">Deal Price *</Label>
              <div className="mt-1 relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="deal_price"
                  type="number"
                  step="0.01"
                  value={formData.deal_price}
                  onChange={(e) => handleInputChange('deal_price', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className="pl-10"
                />
              </div>
              {discountPercentage > 0 && (
                <p className="text-green-600 text-sm mt-1">
                  {discountPercentage}% discount
                </p>
              )}
              {errors.deal_price && (
                <p className="text-red-500 text-sm mt-1">{errors.deal_price}</p>
              )}
            </div>

            {/* Deal Type */}
            <div>
              <Label htmlFor="deal_type">Deal Type</Label>
              <select
                id="deal_type"
                value={formData.deal_type}
                onChange={(e) => handleInputChange('deal_type', e.target.value as DealType)}
                className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="regular">Regular</option>
                <option value="flash">Flash Sale</option>
                <option value="clearance">Clearance</option>
                <option value="seasonal">Seasonal</option>
              </select>
            </div>

            {/* Max Quantity */}
            <div>
              <Label htmlFor="max_quantity">Max Quantity (Optional)</Label>
              <Input
                id="max_quantity"
                type="number"
                value={formData.max_quantity || ''}
                onChange={(e) => handleInputChange('max_quantity', e.target.value ? parseInt(e.target.value) : undefined)}
                placeholder="Unlimited"
                className="mt-1"
              />
              {deal.used_quantity > 0 && (
                <p className="text-sm text-gray-500 mt-1">
                  {deal.used_quantity} already used
                </p>
              )}
            </div>

            {/* Start Date */}
            <div>
              <Label htmlFor="start_date">Start Date *</Label>
              <div className="mt-1 relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => handleInputChange('start_date', e.target.value)}
                  className="pl-10"
                />
              </div>
              {errors.start_date && (
                <p className="text-red-500 text-sm mt-1">{errors.start_date}</p>
              )}
            </div>

            {/* End Date */}
            <div>
              <Label htmlFor="end_date">End Date *</Label>
              <div className="mt-1 relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => handleInputChange('end_date', e.target.value)}
                  className="pl-10"
                />
              </div>
              {errors.end_date && (
                <p className="text-red-500 text-sm mt-1">{errors.end_date}</p>
              )}
            </div>

            {/* Status Options */}
            <div className="md:col-span-2">
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    id="is_active"
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <Label htmlFor="is_active" className="ml-2">
                    Active Deal
                  </Label>
                </div>

                <div className="flex items-center">
                  <input
                    id="featured"
                    type="checkbox"
                    checked={formData.featured}
                    onChange={(e) => handleInputChange('featured', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <Label htmlFor="featured" className="ml-2">
                    Featured Deal
                  </Label>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between">
          <Link href={`/admin/deals/${dealId}`}>
            <Button type="button" variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </Link>

          <Button type="submit" disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </div>
  );
}
