'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminHeader } from '@/components/admin';
import { ArrowLeft, Save } from 'lucide-react';
import Link from 'next/link';

interface FormData {
  name: string;
  description: string;
  estimated_delivery_time: string;
  base_cost: number;
  cost_per_kg: number;
  free_weight_limit: number;
  tracking_supported: boolean;
  countries: string[];
  is_active: boolean;
}

export default function NewCourierServicePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [countriesInput, setCountriesInput] = useState('');
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    estimated_delivery_time: '',
    base_cost: 0,
    cost_per_kg: 0,
    free_weight_limit: 0,
    tracking_supported: true,
    countries: [],
    is_active: true,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Parse countries from comma-separated input
      const countries = countriesInput
        .split(',')
        .map(c => c.trim().toUpperCase())
        .filter(c => c.length > 0);

      const payload = {
        ...formData,
        countries,
      };

      const response = await fetch('/api/admin/courier-services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create courier service');
      }

      router.push('/admin/courier-services');
    } catch (error) {
      console.error('Error creating courier service:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Add New Courier Service"
        description="Create a new international courier service provider"
      />

      <div className="flex items-center gap-4">
        <Link href="/admin/courier-services">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Courier Services
          </Button>
        </Link>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Information */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>
                  Enter the basic details for the courier service
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="name">Service Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., DHL Express, FedEx International"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Brief description of the courier service"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="estimated_delivery_time">Estimated Delivery Time *</Label>
                  <Input
                    id="estimated_delivery_time"
                    value={formData.estimated_delivery_time}
                    onChange={(e) => handleInputChange('estimated_delivery_time', e.target.value)}
                    placeholder="e.g., 3-5 business days, 1-2 weeks"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="countries">Supported Countries *</Label>
                  <Input
                    id="countries"
                    value={countriesInput}
                    onChange={(e) => setCountriesInput(e.target.value)}
                    placeholder="US, CA, GB, DE, FR, AU (comma-separated country codes)"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Enter country codes separated by commas (e.g., US, CA, GB)
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Pricing & Settings */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Pricing & Settings</CardTitle>
                <CardDescription>
                  Configure pricing and service options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="base_cost">Base Cost (USD) *</Label>
                  <Input
                    id="base_cost"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.base_cost}
                    onChange={(e) => handleInputChange('base_cost', parseFloat(e.target.value) || 0)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="cost_per_kg">Cost per KG (USD)</Label>
                  <Input
                    id="cost_per_kg"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.cost_per_kg}
                    onChange={(e) => handleInputChange('cost_per_kg', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div>
                  <Label htmlFor="free_weight_limit">Free Weight Limit (KG)</Label>
                  <Input
                    id="free_weight_limit"
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.free_weight_limit}
                    onChange={(e) => handleInputChange('free_weight_limit', parseFloat(e.target.value) || 0)}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Weight up to this limit won't incur additional charges
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="tracking_supported"
                    checked={formData.tracking_supported}
                    onCheckedChange={(checked) => handleInputChange('tracking_supported', checked)}
                  />
                  <Label htmlFor="tracking_supported">Tracking Supported</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end mt-6">
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>Saving...</>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Courier Service
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}