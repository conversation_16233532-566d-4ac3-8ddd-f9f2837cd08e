"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/lib/hooks/use-toast";
import { createSPASassClient } from "@/lib/supabase/client";
import { CheckoutService } from "@/lib/services/checkout";
import { useCart } from "@/lib/context/CartContext";
import EcommerceLayout from "@/components/ecommerce/EcommerceLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Truck, MapPin, CreditCard, Package } from "lucide-react";
import { formatCurrency } from "@/utils";
import Link from "next/link";
import { ExternalDeliveryService } from "@/features/admin/types";
import { CourierService } from "@/features/products/types";
import Confetti from "@/components/Confetti";
import { PAYMENT_CONFIG, validateTransactionId } from "@/lib/config/payment";
import PaymentMethodSelector from "@/components/checkout/PaymentMethodSelector";
import WavePayment from "@/components/checkout/WavePayment";
import CryptoPayment from "@/components/checkout/CryptoPayment";
import BankTransferPayment from "@/components/checkout/BankTransferPayment";
import DeliveryMethodSelector from "@/components/checkout/DeliveryMethodSelector";

// Simplified step types
type CheckoutStep =
  | "cart"
  | "delivery"
  | "address"
  | "payment"
  | "payment-instructions"
  | "payment-confirmation"
  | "confirmation";
type PaymentMethod = "wave" | "crypto" | "bank-transfer";
type CryptoType = "bitcoin" | "ethereum" | "usdt";
type DeliveryMethod = "delivery" | "pickup" | "courier";

interface ShippingAddress {
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

export default function NewCheckoutPage() {
  const { cartItems, loading, refreshCart } = useCart();
  const [userId, setUserId] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<CheckoutStep>("cart");

  // Delivery related state
  const [deliveryMethod, setDeliveryMethod] =
    useState<DeliveryMethod>("delivery");
  const [deliveryProviders, setDeliveryProviders] = useState<
    ExternalDeliveryService[]
  >([]);
  const [courierServices, setCourierServices] = useState<CourierService[]>([]);
  const [selectedProviderId, setSelectedProviderId] = useState<string | null>(
    null
  );

  // Check if cart contains international products
  const hasInternationalProducts = cartItems.some(
    (item) => item.product && !(item.product as any).is_local
  );
  const isInternationalOrder = hasInternationalProducts;

  // Address state
  const [shippingAddress, setShippingAddress] =
    useState<ShippingAddress | null>(null);
  const [savedAddresses, setSavedAddresses] = useState<ShippingAddress[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(
    null
  );
  const [showAddressForm, setShowAddressForm] = useState(false);

  // Payment state
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(
    null
  );
  const [selectedCrypto, setSelectedCrypto] = useState<CryptoType>("bitcoin");
  const [transactionId, setTransactionId] = useState<string>("");

  // Order state
  const [orderId, setOrderId] = useState<string | null>(null);
  const [orderStatus, setOrderStatus] = useState<string>("pending");

  const { toast } = useToast();
  const router = useRouter();

  // Helper function to get effective price (considering flash sale prices)
  const getEffectivePrice = (product: any) => {
    if (product?.compare_at_price && product.compare_at_price > product.price) {
      return product.price; // Already discounted price
    }
    return product?.price || 0;
  };

  // Calculate totals
  const subtotal = cartItems.reduce((total, item) => {
    return total + getEffectivePrice(item.product) * item.quantity;
  }, 0);

  const currency = "GMD";
  const [serviceFee, setServiceFee] = useState(0);

  // Calculate service fee asynchronously
  useEffect(() => {
    const calculateFee = async () => {
      try {
        const fee = await CheckoutService.calculateServiceFee(subtotal);
        setServiceFee(fee);
      } catch (error) {
        console.error("Error calculating service fee:", error);
        // Fallback to sync calculation
        setServiceFee(CheckoutService.calculateServiceFeeSync(subtotal));
      }
    };
    calculateFee();
  }, [subtotal]);

  // Auto-set delivery method based on order type
  useEffect(() => {
    if (isInternationalOrder && deliveryMethod === "delivery") {
      setDeliveryMethod("courier");
    }
  }, [isInternationalOrder, deliveryMethod]);

  // Calculate delivery fee based on method and provider
  let deliveryFee = 0;
  if (deliveryMethod === "pickup") {
    deliveryFee = 0; // Free pickup
  } else if (deliveryMethod === "delivery" && selectedProviderId) {
    // Local delivery service
    const selectedProvider = deliveryProviders.find(
      (p) => p.id === selectedProviderId
    );
    deliveryFee = selectedProvider?.base_fee || 0;
  } else if (deliveryMethod === "courier" && selectedProviderId) {
    // Courier service (for international shipping)
    const selectedCourier = courierServices.find(
      (c) => c.id === selectedProviderId
    );
    deliveryFee = (selectedCourier as any)?.base_cost || 0;
  }

  const total = subtotal + serviceFee + deliveryFee;

  // Fetch saved addresses
  const fetchSavedAddresses = async (userId: string) => {
    try {
      const supabase = await createSPASassClient();
      const { data: profile, error } = await supabase
        .getSupabaseClient()
        .from("profiles")
        .select(
          "first_name, last_name, email, phone, address, city, state, country, postal_code"
        )
        .eq("id", userId)
        .single();

      if (error) throw error;

      // Check if user has any address information filled out
      const hasAddressInfo =
        profile &&
        (profile.address ||
          profile.city ||
          profile.state ||
          profile.country ||
          profile.postal_code);

      if (hasAddressInfo) {
        const savedAddress: ShippingAddress = {
          name: `${profile.first_name || ""} ${profile.last_name || ""}`.trim(),
          email: profile.email || "",
          phone: profile.phone || "",
          address: profile.address || "",
          city: profile.city || "",
          state: profile.state || "",
          country: profile.country || "The Gambia",
          postalCode: profile.postal_code || "",
        };
        setSavedAddresses([savedAddress]);
      } else {
        setSavedAddresses([]);
      }
    } catch (error) {
      console.error("Error fetching saved addresses:", error);
      setSavedAddresses([]);
    }
  };

  // Authentication check
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = await createSPASassClient();
        const {
          data: { user },
        } = await supabase.getSupabaseClient().auth.getUser();

        if (user) {
          setUserId(user.id);
          await fetchSavedAddresses(user.id);
        } else {
          router.push("/auth/login?redirect=/checkout");
        }
      } catch (error) {
        console.error("Error checking auth:", error);
        toast({
          title: "Error",
          description: "Failed to verify your account. Please try again.",
          variant: "destructive",
        });
      }
    };

    checkAuth();
  }, [toast, router]);

  // Fetch delivery providers and courier services
  useEffect(() => {
    const fetchShippingServices = async () => {
      try {
        // Reset selection when switching between order types
        setSelectedProviderId(null);

        // Auto-set delivery method based on product type
        if (isInternationalOrder && deliveryMethod !== "courier") {
          setDeliveryMethod("courier"); // International products must use courier
        } else if (!isInternationalOrder && deliveryMethod === "courier") {
          setDeliveryMethod("delivery"); // Local products cannot use courier
        }

        if (!isInternationalOrder) {
          // Fetch delivery services for local products
          const response = await fetch(
            "/api/admin/delivery-services?is_active=true&per_page=100"
          );
          if (!response.ok)
            throw new Error("Failed to fetch delivery providers");

          const data = await response.json();
          const providers = data.services || [];
          setDeliveryProviders(providers);
          setCourierServices([]); // Clear courier services

          // Auto-select first provider if available and delivery is selected
          if (providers.length > 0 && deliveryMethod === "delivery") {
            setSelectedProviderId(providers[0].id);
          }
        } else {
          // Fetch courier services for international products
          const response = await fetch(
            "/api/admin/courier-services?is_active=true&per_page=100"
          );
          if (!response.ok) throw new Error("Failed to fetch courier services");

          const data = await response.json();
          const services = data.data || []; // Courier services API returns data.data, not data.services
          setCourierServices(services);
          setDeliveryProviders([]); // Clear delivery services

          // Auto-select first courier service if available and courier is selected
          if (services.length > 0 && deliveryMethod === "courier") {
            setSelectedProviderId(services[0].id);
          }
        }
      } catch (error) {
        console.error("Error fetching shipping services:", error);
        toast({
          title: "Error",
          description: `Could not load ${
            isInternationalOrder ? "courier" : "delivery"
          } options.`,
          variant: "destructive",
        });
        setDeliveryProviders([]);
        setCourierServices([]);
      }
    };

    fetchShippingServices();
  }, [deliveryMethod, isInternationalOrder, toast]);

  // Step navigation functions
  const goToStep = (step: CheckoutStep) => {
    setCurrentStep(step);
  };

  const goBack = () => {
    switch (currentStep) {
      case "delivery":
        setCurrentStep("cart");
        break;
      case "address":
        setCurrentStep("delivery");
        break;
      case "payment":
        if (deliveryMethod === "pickup") {
          setCurrentStep("delivery");
        } else {
          setCurrentStep("address");
        }
        break;
      case "payment-instructions":
        setCurrentStep("payment");
        break;
      case "payment-confirmation":
        setCurrentStep("payment-instructions");
        break;
      case "confirmation":
        // Don't allow going back from confirmation
        break;
      default:
        // Fallback to cart
        setCurrentStep("cart");
    }
  };

  const canProceedFromDelivery = () => {
    return (
      deliveryMethod === "pickup" ||
      ((deliveryMethod === "delivery" || deliveryMethod === "courier") &&
        selectedProviderId)
    );
  };

  const proceedFromDelivery = () => {
    if (!canProceedFromDelivery()) return;

    if (deliveryMethod === "pickup") {
      goToStep("payment"); // Skip address for pickup
    } else {
      goToStep("address"); // Go to address for delivery and courier
    }
  };

  const handleSelectSavedAddress = (address: ShippingAddress) => {
    setShippingAddress(address);
    setSelectedAddressId("saved");
    setShowAddressForm(false);
  };

  const handleUseNewAddress = () => {
    setSelectedAddressId("new");
    setShowAddressForm(true);
    setShippingAddress(null);
  };

  const proceedFromAddress = () => {
    if (!shippingAddress) {
      toast({
        title: "Address Required",
        description: "Please select or enter a delivery address.",
        variant: "destructive",
      });
      return;
    }
    goToStep("payment");
  };

  const handleDeliveryMethodChange = (method: DeliveryMethod) => {
    if (process.env.NODE_ENV === "development") {
      console.log("[Checkout] Delivery method changed:", {
        method,
        isInternationalOrder,
      });
    }
    setDeliveryMethod(method);
    if (method === "pickup") {
      setSelectedProviderId(null);
    } else if (method === "delivery") {
      // Auto-select first available delivery service for domestic orders
      if (!isInternationalOrder && deliveryProviders.length > 0) {
        setSelectedProviderId(deliveryProviders[0].id);
      } else {
        setSelectedProviderId(null);
      }
    } else if (method === "courier") {
      // Auto-select first available courier service
      if (courierServices.length > 0) {
        setSelectedProviderId(courierServices[0].id);
      } else {
        setSelectedProviderId(null);
      }
    }
  };

  const proceedToPaymentInstructions = () => {
    if (!paymentMethod) return;
    goToStep("payment-instructions");
  };

  const proceedToPaymentConfirmation = () => {
    goToStep("payment-confirmation");
  };

  const handlePlaceOrder = async () => {
    try {
      if (!userId) throw new Error("User not authenticated");
      if (!paymentMethod) throw new Error("Payment method not selected");
      if (!transactionId.trim()) throw new Error("Transaction ID is required");

      // Validate transaction ID format
      if (!validateTransactionId(paymentMethod, transactionId)) {
        throw new Error(
          `Invalid transaction ID format for ${paymentMethod} payment`
        );
      }

      // Create the order
      const order = await CheckoutService.createOrder({
        userId,
        cartItems,
        subtotal,
        serviceFee,
        deliveryFee,
        total,
        currency,
        deliveryMethod,
        selected_delivery_service_id:
          deliveryMethod === "delivery" || deliveryMethod === "courier"
            ? selectedProviderId
            : null,
        shippingAddress:
          deliveryMethod === "delivery" || deliveryMethod === "courier"
            ? shippingAddress || undefined
            : undefined,
      });

      // Create payment record with user-provided transaction ID
      await CheckoutService.createPayment({
        orderId: order.id,
        amount: total,
        currency,
        paymentMethod,
        transactionId: transactionId.trim(),
      });

      // Refresh cart and show success
      await refreshCart();
      setOrderId(order.id);
      setOrderStatus(order.status);
      goToStep("confirmation");

      toast({
        title: "Order Confirmed!",
        description: `Your order #${order.id.substring(
          0,
          8
        )} has been placed successfully.`,
        variant: "success",
      });
    } catch (error) {
      console.error("Error placing order:", error);
      toast({
        title: "Error",
        description: "Failed to place order. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Empty cart check
  if (!loading && cartItems.length === 0 && currentStep !== "confirmation") {
    return (
      <EcommerceLayout>
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <Package className="mx-auto h-16 w-16 text-gray-400 mb-4" />
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Your cart is empty
            </h2>
            <p className="text-gray-600 mb-8">
              Add some items to your cart to continue
            </p>
            <Link href="/products">
              <Button className="bg-primary-600 hover:bg-primary-700">
                Continue Shopping
              </Button>
            </Link>
          </div>
        </div>
      </EcommerceLayout>
    );
  }

  return (
    <EcommerceLayout>
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            {[
              { step: "cart", label: "Cart", icon: Package },
              { step: "delivery", label: "Delivery", icon: Truck },
              {
                step: "address",
                label: "Address",
                icon: MapPin,
                conditional: deliveryMethod === "delivery",
              },
              { step: "payment", label: "Payment", icon: CreditCard },
            ]
              .filter((item) => !item.conditional || item.conditional)
              .map((item, index, array) => {
                const stepOrder = [
                  "cart",
                  "delivery",
                  "address",
                  "payment",
                  "payment-instructions",
                  "payment-confirmation",
                  "confirmation",
                ];
                const currentIndex = stepOrder.indexOf(currentStep);
                const itemIndex = stepOrder.indexOf(item.step);

                const isActive =
                  currentStep === item.step ||
                  (item.step === "payment" &&
                    ["payment-instructions", "payment-confirmation"].includes(
                      currentStep
                    ));
                const isCompleted = currentIndex > itemIndex;

                return (
                  <div key={item.step} className="flex items-center">
                    <div
                      className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                        isActive
                          ? "border-primary-600 bg-primary-600 text-white"
                          : isCompleted
                          ? "border-green-600 bg-green-600 text-white"
                          : "border-gray-300 bg-white text-gray-400"
                      }`}
                    >
                      <item.icon className="h-5 w-5" />
                    </div>
                    <span
                      className={`ml-2 text-sm font-medium ${
                        isActive
                          ? "text-primary-600"
                          : isCompleted
                          ? "text-green-600"
                          : "text-gray-400"
                      }`}
                    >
                      {item.label}
                    </span>
                    {index < array.length - 1 && (
                      <div
                        className={`ml-4 w-8 h-0.5 ${
                          isCompleted ? "bg-green-600" : "bg-gray-300"
                        }`}
                      />
                    )}
                  </div>
                );
              })}
          </div>
        </div>

        <div
          className={`grid gap-8 ${
            currentStep === "confirmation"
              ? "grid-cols-1"
              : "grid-cols-1 lg:grid-cols-3"
          }`}
        >
          {/* Main Content */}
          <div
            className={currentStep === "confirmation" ? "" : "lg:col-span-2"}
          >
            {/* Step 1: Cart Review */}
            {currentStep === "cart" && (
              <Card>
                <CardHeader>
                  <CardTitle>Review Your Order</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {cartItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center space-x-4 py-4 border-b"
                      >
                        <div className="flex-1">
                          <h3 className="font-medium">{item.product?.name}</h3>
                          <div className="flex items-center space-x-2 mt-1">
                            {item.product?.compare_at_price &&
                            item.product.compare_at_price >
                              item.product.price ? (
                              <>
                                <span className="text-red-600 font-medium">
                                  {formatCurrency(item.product.price, currency)}
                                </span>
                                <span className="text-gray-500 line-through text-sm">
                                  {formatCurrency(
                                    item.product.compare_at_price,
                                    currency
                                  )}
                                </span>
                                <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                  {Math.round(
                                    ((item.product.compare_at_price -
                                      item.product.price) /
                                      item.product.compare_at_price) *
                                      100
                                  )}
                                  % OFF
                                </span>
                              </>
                            ) : (
                              <span className="font-medium">
                                {formatCurrency(
                                  getEffectivePrice(item.product),
                                  currency
                                )}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-600">
                            Qty: {item.quantity}
                          </p>
                          <p className="font-medium">
                            {formatCurrency(
                              getEffectivePrice(item.product) * item.quantity,
                              currency
                            )}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* International Product Indicator */}
                  {isInternationalOrder && (
                    <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <span className="text-blue-600">✈️</span>
                        <span className="text-sm font-medium text-blue-800">
                          International shipping required - This order contains
                          international products
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-center pt-6">
                    <Link href="/cart">
                      <Button variant="outline">
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Edit Cart
                      </Button>
                    </Link>
                    <Button
                      onClick={() => goToStep("delivery")}
                      className="bg-primary-600 hover:bg-primary-700"
                    >
                      Continue to Delivery
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 2: Delivery Method */}
            {currentStep === "delivery" && (
              <DeliveryMethodSelector
                selectedMethod={deliveryMethod}
                onMethodChange={handleDeliveryMethodChange}
                deliveryProviders={deliveryProviders}
                courierServices={courierServices}
                selectedProviderId={selectedProviderId}
                onProviderSelect={setSelectedProviderId}
                onContinue={proceedFromDelivery}
                isInternationalProduct={isInternationalOrder}
              />
            )}

            {/* Step 3: Address (only for delivery and courier) */}
            {currentStep === "address" &&
              (deliveryMethod === "delivery" ||
                deliveryMethod === "courier") && (
                <Card>
                  <CardHeader>
                    <CardTitle>
                      {deliveryMethod === "courier"
                        ? "Shipping Address"
                        : "Delivery Address"}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Saved Addresses */}
                    {savedAddresses.length > 0 && (
                      <div className="space-y-4">
                        <h3 className="text-lg font-medium">Saved Addresses</h3>
                        {savedAddresses.map((address, index) => (
                          <div
                            key={index}
                            className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                              selectedAddressId === "saved" &&
                              shippingAddress === address
                                ? "border-primary-600 bg-primary-50"
                                : "border-gray-200 hover:border-gray-300"
                            }`}
                            onClick={() => handleSelectSavedAddress(address)}
                          >
                            <div className="flex items-start justify-between">
                              <div>
                                <p className="font-medium">
                                  {address.name || "No name provided"}
                                </p>
                                {address.phone && (
                                  <p className="text-sm text-gray-600">
                                    {address.phone}
                                  </p>
                                )}
                                {address.address && (
                                  <p className="text-sm text-gray-600">
                                    {address.address}
                                  </p>
                                )}
                                <p className="text-sm text-gray-600">
                                  {[
                                    address.city,
                                    address.state,
                                    address.postalCode,
                                    address.country,
                                  ]
                                    .filter(Boolean)
                                    .join(", ")}
                                </p>
                              </div>
                              {selectedAddressId === "saved" &&
                                shippingAddress === address && (
                                  <div className="text-primary-600">
                                    <svg
                                      className="w-5 h-5"
                                      fill="currentColor"
                                      viewBox="0 0 20 20"
                                    >
                                      <path
                                        fillRule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                  </div>
                                )}
                            </div>
                          </div>
                        ))}

                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleUseNewAddress}
                          className="w-full"
                        >
                          Use a Different Address
                        </Button>
                      </div>
                    )}

                    {/* New Address Form */}
                    {(savedAddresses.length === 0 || showAddressForm) && (
                      <div className="space-y-4">
                        {savedAddresses.length > 0 && (
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium">New Address</h3>
                            <Button
                              type="button"
                              variant="ghost"
                              onClick={() => setShowAddressForm(false)}
                              className="text-sm"
                            >
                              Cancel
                            </Button>
                          </div>
                        )}

                        <form
                          onSubmit={(e) => {
                            e.preventDefault();
                            const formData = new FormData(
                              e.target as HTMLFormElement
                            );
                            const address: ShippingAddress = {
                              name: formData.get("name") as string,
                              email: formData.get("email") as string,
                              phone: formData.get("phone") as string,
                              address: formData.get("address") as string,
                              city: formData.get("city") as string,
                              state: formData.get("state") as string,
                              country: formData.get("country") as string,
                              postalCode: formData.get("postalCode") as string,
                            };
                            setShippingAddress(address);
                            proceedFromAddress();
                          }}
                          className="space-y-4"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Full Name
                              </label>
                              <input
                                type="text"
                                name="name"
                                required
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Email
                              </label>
                              <input
                                type="email"
                                name="email"
                                required
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                              />
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Phone Number
                            </label>
                            <input
                              type="tel"
                              name="phone"
                              required
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Street Address
                            </label>
                            <textarea
                              name="address"
                              required
                              rows={3}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                City
                              </label>
                              <input
                                type="text"
                                name="city"
                                required
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                State
                              </label>
                              <input
                                type="text"
                                name="state"
                                required
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Postal Code
                              </label>
                              <input
                                type="text"
                                name="postalCode"
                                required
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                              />
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Country
                            </label>
                            <input
                              type="text"
                              name="country"
                              required
                              defaultValue="Gambia"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                            />
                          </div>

                          <div className="flex justify-between items-center pt-4 border-t">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={goBack}
                            >
                              <ArrowLeft className="h-4 w-4 mr-2" />
                              Back
                            </Button>
                            <Button
                              type="submit"
                              className="bg-primary-600 hover:bg-primary-700"
                            >
                              Continue to Payment
                            </Button>
                          </div>
                        </form>
                      </div>
                    )}

                    {/* Continue button for saved address */}
                    {savedAddresses.length > 0 &&
                      !showAddressForm &&
                      shippingAddress && (
                        <div className="flex justify-between items-center pt-4 border-t">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={goBack}
                          >
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back
                          </Button>
                          <Button
                            onClick={proceedFromAddress}
                            className="bg-primary-600 hover:bg-primary-700"
                          >
                            Continue to Payment
                          </Button>
                        </div>
                      )}
                  </CardContent>
                </Card>
              )}

            {/* Step 4: Payment */}
            {currentStep === "payment" && (
              <Card>
                <CardHeader>
                  <CardTitle>Payment Method</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {/* Wave Payment */}
                      <div
                        onClick={() => setPaymentMethod("wave")}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          paymentMethod === "wave"
                            ? "border-primary-600 bg-primary-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        <div className="text-center">
                          <CreditCard className="h-8 w-8 mx-auto text-blue-600 mb-2" />
                          <h3 className="font-medium">Wave Payment</h3>
                          <p className="text-sm text-gray-600">
                            Pay with Wave Money
                          </p>
                        </div>
                      </div>

                      {/* Crypto Payment */}
                      <div
                        onClick={() => setPaymentMethod("crypto")}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          paymentMethod === "crypto"
                            ? "border-primary-600 bg-primary-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        <div className="text-center">
                          <div className="h-8 w-8 mx-auto bg-orange-500 rounded-full flex items-center justify-center mb-2">
                            <span className="text-white font-bold text-sm">
                              ₿
                            </span>
                          </div>
                          <h3 className="font-medium">Crypto Payment</h3>
                          <p className="text-sm text-gray-600">
                            Pay with cryptocurrency
                          </p>
                        </div>
                      </div>

                      {/* Bank Transfer Payment */}
                      <div
                        onClick={() => setPaymentMethod("bank-transfer")}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                          paymentMethod === "bank-transfer"
                            ? "border-primary-600 bg-primary-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        <div className="text-center">
                          <div className="h-8 w-8 mx-auto bg-green-500 rounded-full flex items-center justify-center mb-2">
                            <span className="text-white font-bold text-sm">
                              🏦
                            </span>
                          </div>
                          <h3 className="font-medium">Bank Transfer</h3>
                          <p className="text-sm text-gray-600">
                            Direct bank transfer
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-between items-center pt-4 border-t">
                      <Button variant="outline" onClick={goBack}>
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back
                      </Button>
                      <Button
                        onClick={proceedToPaymentInstructions}
                        disabled={!paymentMethod}
                        className="bg-primary-600 hover:bg-primary-700"
                      >
                        Continue to Payment Instructions
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 4a: Payment Instructions */}
            {currentStep === "payment-instructions" && paymentMethod && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="h-6 w-6" />
                    <span>Payment Instructions</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <span className="text-2xl">⚠️</span>
                      <div>
                        <h3 className="font-semibold text-yellow-800 mb-1">
                          Important Instructions
                        </h3>
                        <p className="text-sm text-yellow-700">
                          Please follow these steps carefully to complete your
                          payment:
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Payment Method Specific Instructions */}
                  {paymentMethod === "wave" && (
                    <div className="space-y-4">
                      <div className="text-center">
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">
                          Pay with Wave Money
                        </h3>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                          <div className="mb-4">
                            <p className="text-lg font-medium text-blue-900 mb-2">
                              Amount to Pay:{" "}
                              <span className="text-2xl font-bold text-blue-600">
                                {formatCurrency(total, currency)}
                              </span>
                            </p>
                          </div>

                          {/* QR Code Placeholder */}
                          <div className="mb-4">
                            <div className="w-48 h-48 mx-auto bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center">
                              <div className="text-center">
                                <div className="w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                                  <span className="text-4xl">📱</span>
                                </div>
                                <p className="text-sm text-gray-600">
                                  Wave QR Code
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Wave Number */}
                          <div className="space-y-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Wave Number:
                              </label>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="text"
                                  value={PAYMENT_CONFIG.wave.number}
                                  readOnly
                                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-center font-mono text-lg"
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    navigator.clipboard.writeText(
                                      PAYMENT_CONFIG.wave.number
                                    );
                                    toast({
                                      title: "Copied!",
                                      description:
                                        "Wave number copied to clipboard",
                                    });
                                  }}
                                >
                                  Copy
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                        <h4 className="font-semibold text-blue-900 mb-3">
                          How to Pay:
                        </h4>
                        <ol className="list-decimal list-inside space-y-2 text-sm text-blue-800">
                          <li>Open your Wave Money app</li>
                          <li>Scan the QR code above OR use the Wave number</li>
                          <li>
                            Enter the exact amount:{" "}
                            <strong>{formatCurrency(total, currency)}</strong>
                          </li>
                          <li>Complete the transfer</li>
                          <li>
                            Copy the transaction ID from your transfer
                            confirmation
                          </li>
                          <li>
                            Click "I've Made Payment" below and paste the
                            transaction ID
                          </li>
                        </ol>
                      </div>
                    </div>
                  )}

                  {paymentMethod === "crypto" && (
                    <div className="space-y-4">
                      <div className="text-center">
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">
                          Pay with Cryptocurrency
                        </h3>
                        <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                          <div className="mb-4">
                            <p className="text-lg font-medium text-orange-900 mb-2">
                              Amount to Pay:{" "}
                              <span className="text-2xl font-bold text-orange-600">
                                {formatCurrency(total, currency)}
                              </span>
                            </p>
                            <p className="text-sm text-orange-700">
                              ≈ $20.00 USD (estimate)
                            </p>
                          </div>

                          {/* QR Code Placeholder */}
                          <div className="mb-4">
                            <div className="w-48 h-48 mx-auto bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center">
                              <div className="text-center">
                                <div className="w-32 h-32 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                                  <span className="text-4xl">₿</span>
                                </div>
                                <p className="text-sm text-gray-600">
                                  Bitcoin QR Code
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Wallet Address */}
                          <div className="space-y-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Bitcoin Wallet Address:
                              </label>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="text"
                                  value={PAYMENT_CONFIG.crypto.bitcoin.address}
                                  readOnly
                                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 font-mono text-sm"
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    navigator.clipboard.writeText(
                                      PAYMENT_CONFIG.crypto.bitcoin.address
                                    );
                                    toast({
                                      title: "Copied!",
                                      description:
                                        "Wallet address copied to clipboard",
                                    });
                                  }}
                                >
                                  Copy
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div className="bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200 rounded-lg p-4">
                        <h4 className="font-semibold text-orange-900 mb-3">
                          How to Pay:
                        </h4>
                        <ol className="list-decimal list-inside space-y-2 text-sm text-orange-800">
                          <li>Open your Bitcoin wallet app</li>
                          <li>
                            Scan the QR code above OR copy the wallet address
                          </li>
                          <li>
                            Send the equivalent of{" "}
                            <strong>{formatCurrency(total, currency)}</strong>{" "}
                            in Bitcoin
                          </li>
                          <li>Wait for the transaction to be confirmed</li>
                          <li>Copy the transaction hash/ID from your wallet</li>
                          <li>
                            Click "I've Made Payment" below and paste the
                            transaction ID
                          </li>
                        </ol>
                      </div>
                    </div>
                  )}

                  {paymentMethod === "bank-transfer" && (
                    <div className="space-y-4">
                      <div className="text-center">
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">
                          Pay via Bank Transfer
                        </h3>
                        <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                          <div className="mb-4">
                            <p className="text-lg font-medium text-green-900 mb-2">
                              Amount to Transfer:{" "}
                              <span className="text-2xl font-bold text-green-600">
                                {formatCurrency(total, currency)}
                              </span>
                            </p>
                          </div>

                          {/* Bank Details */}
                          <div className="bg-white border border-green-200 rounded-lg p-4 mb-4">
                            <h4 className="font-semibold text-green-900 mb-3">
                              Bank Account Details:
                            </h4>
                            <div className="space-y-3 text-sm">
                              <div className="flex justify-between items-center">
                                <span className="font-medium text-gray-600">
                                  Bank Name:
                                </span>
                                <div className="flex items-center space-x-2">
                                  <span className="font-mono">
                                    Trust Bank Limited
                                  </span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      navigator.clipboard.writeText(
                                        "Trust Bank Limited"
                                      );
                                      toast({
                                        title: "Copied!",
                                        description:
                                          "Bank name copied to clipboard",
                                      });
                                    }}
                                  >
                                    Copy
                                  </Button>
                                </div>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="font-medium text-gray-600">
                                  Account Name:
                                </span>
                                <div className="flex items-center space-x-2">
                                  <span className="font-mono">
                                    Finder Marketplace Ltd
                                  </span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      navigator.clipboard.writeText(
                                        "Finder Marketplace Ltd"
                                      );
                                      toast({
                                        title: "Copied!",
                                        description:
                                          "Account name copied to clipboard",
                                      });
                                    }}
                                  >
                                    Copy
                                  </Button>
                                </div>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="font-medium text-gray-600">
                                  Account Number:
                                </span>
                                <div className="flex items-center space-x-2">
                                  <span className="font-mono font-bold">
                                    **********
                                  </span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      navigator.clipboard.writeText(
                                        "**********"
                                      );
                                      toast({
                                        title: "Copied!",
                                        description:
                                          "Account number copied to clipboard",
                                      });
                                    }}
                                  >
                                    Copy
                                  </Button>
                                </div>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="font-medium text-gray-600">
                                  SWIFT Code:
                                </span>
                                <div className="flex items-center space-x-2">
                                  <span className="font-mono">TBLGMGM1</span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      navigator.clipboard.writeText("TBLGMGM1");
                                      toast({
                                        title: "Copied!",
                                        description:
                                          "SWIFT code copied to clipboard",
                                      });
                                    }}
                                  >
                                    Copy
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                        <h4 className="font-semibold text-green-900 mb-3">
                          How to Transfer:
                        </h4>
                        <ol className="list-decimal list-inside space-y-2 text-sm text-green-800">
                          <li>
                            Open your mobile banking app or visit your bank
                          </li>
                          <li>Select "Transfer" or "Send Money"</li>
                          <li>Enter the bank account details above</li>
                          <li>
                            Transfer exactly{" "}
                            <strong>{formatCurrency(total, currency)}</strong>
                          </li>
                          <li>Save your transaction reference/receipt</li>
                          <li>Click "I've Made Payment" below</li>
                          <li>
                            Enter your transaction reference when prompted
                          </li>
                        </ol>
                      </div>

                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h4 className="font-semibold text-yellow-800 mb-2">
                          Important Notes:
                        </h4>
                        <ul className="list-disc list-inside space-y-1 text-sm text-yellow-700">
                          <li>
                            Transfer the exact amount:{" "}
                            <strong>{formatCurrency(total, currency)}</strong>
                          </li>
                          <li>
                            Include your order reference in the transfer
                            description if possible
                          </li>
                          <li>
                            Bank transfers may take 1-3 business days to process
                          </li>
                          <li>
                            Keep your transaction receipt for verification
                          </li>
                        </ul>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-center pt-4 border-t">
                    <Button variant="outline" onClick={goBack}>
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back
                    </Button>
                    <Button
                      onClick={proceedToPaymentConfirmation}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      I've Made Payment ✓
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 4b: Payment Confirmation */}
            {currentStep === "payment-confirmation" && paymentMethod && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <span className="text-2xl">✅</span>
                    <span>Confirm Your Payment</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <span className="text-2xl">🎉</span>
                      <div>
                        <h3 className="font-semibold text-green-800 mb-1">
                          Almost Done!
                        </h3>
                        <p className="text-sm text-green-700">
                          Please enter your transaction ID to complete your
                          order.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Order Summary */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">
                      Payment Summary:
                    </h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Payment Method:</span>
                        <span className="font-medium capitalize">
                          {paymentMethod === "bank-transfer"
                            ? "Bank Transfer"
                            : paymentMethod === "crypto"
                            ? "Cryptocurrency"
                            : `${paymentMethod} Money`}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Amount Paid:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(total, currency)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Transaction ID Input */}
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Transaction ID <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={transactionId}
                        onChange={(e) => setTransactionId(e.target.value)}
                        placeholder={
                          paymentMethod === "wave"
                            ? "Enter Wave transaction ID (numbers only)"
                            : paymentMethod === "crypto"
                            ? "Enter Bitcoin transaction hash (64 characters)"
                            : paymentMethod === "bank-transfer"
                            ? "Enter bank transaction reference"
                            : "Enter transaction ID"
                        }
                        className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 ${
                          transactionId &&
                          !validateTransactionId(paymentMethod, transactionId)
                            ? "border-red-300 focus:ring-red-500 bg-red-50"
                            : transactionId &&
                              validateTransactionId(
                                paymentMethod,
                                transactionId
                              )
                            ? "border-green-300 focus:ring-green-500 bg-green-50"
                            : "border-gray-300 focus:ring-primary-500"
                        }`}
                      />
                      {transactionId &&
                        !validateTransactionId(
                          paymentMethod,
                          transactionId
                        ) && (
                          <p className="text-xs text-red-600 mt-1">
                            {paymentMethod === "wave"
                              ? "Transaction ID should contain only numbers"
                              : paymentMethod === "crypto"
                              ? "Transaction hash should be 64 characters long (letters and numbers)"
                              : paymentMethod === "bank-transfer"
                              ? "Please enter a valid transaction reference"
                              : "Please enter a valid transaction ID"}
                          </p>
                        )}
                      {transactionId &&
                        validateTransactionId(paymentMethod, transactionId) && (
                          <p className="text-xs text-green-600 mt-1">
                            ✓ Valid transaction ID format
                          </p>
                        )}
                      <p className="text-xs text-gray-500 mt-1">
                        {paymentMethod === "wave"
                          ? "You can find this in your Wave Money transaction history"
                          : paymentMethod === "crypto"
                          ? "You can find this in your Bitcoin wallet transaction history"
                          : paymentMethod === "bank-transfer"
                          ? "You can find this in your bank transaction receipt or mobile banking app"
                          : "You can find this in your transaction history"}
                      </p>
                    </div>

                    {/* Helpful Tips */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-900 mb-2">
                        💡 Tips:
                      </h4>
                      <ul className="list-disc list-inside space-y-1 text-sm text-blue-800">
                        {paymentMethod === "wave" ? (
                          <>
                            <li>
                              Check your Wave Money app for the transaction
                            </li>
                            <li>
                              The transaction ID is usually a series of numbers
                              and letters
                            </li>
                            <li>
                              Make sure you've sent the exact amount:{" "}
                              {formatCurrency(total, currency)}
                            </li>
                          </>
                        ) : paymentMethod === "crypto" ? (
                          <>
                            <li>
                              Check your Bitcoin wallet for the transaction
                            </li>
                            <li>
                              The transaction hash is a long string of letters
                              and numbers
                            </li>
                            <li>
                              Wait for at least 1 confirmation before submitting
                            </li>
                          </>
                        ) : paymentMethod === "bank-transfer" ? (
                          <>
                            <li>
                              Check your mobile banking app or transaction
                              receipt
                            </li>
                            <li>
                              The reference is usually provided after completing
                              the transfer
                            </li>
                            <li>
                              Make sure you've transferred the exact amount:{" "}
                              {formatCurrency(total, currency)}
                            </li>
                            <li>
                              Bank transfers may take 1-3 business days to
                              process
                            </li>
                          </>
                        ) : (
                          <>
                            <li>Check your transaction history</li>
                            <li>
                              Make sure you've sent the exact amount:{" "}
                              {formatCurrency(total, currency)}
                            </li>
                          </>
                        )}
                      </ul>
                    </div>
                  </div>

                  <div className="flex justify-between items-center pt-4 border-t">
                    <Button variant="outline" onClick={goBack}>
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back to Instructions
                    </Button>
                    <Button
                      onClick={handlePlaceOrder}
                      disabled={
                        !transactionId.trim() ||
                        !validateTransactionId(paymentMethod, transactionId)
                      }
                      className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      Complete Order - {formatCurrency(total, currency)}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 5: Confirmation */}
            {currentStep === "confirmation" && orderId && (
              <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 -mx-4 -mt-4 px-4 pt-8">
                <Confetti active={true} />

                <div className="max-w-2xl mx-auto">
                  <div className="bg-white p-8 rounded-2xl shadow-lg border border-green-200">
                    <div className="text-center">
                      {/* Success Icon */}
                      <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-green-100 mb-8">
                        <svg
                          className="h-12 w-12 text-green-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M5 13l4 4L19 7"
                          ></path>
                        </svg>
                      </div>

                      <h1 className="text-4xl font-bold text-green-600 mb-4">
                        Order Confirmed!
                      </h1>
                      <p className="text-xl text-gray-700 mb-4">
                        🎉 Thank you for your purchase!
                      </p>
                      <p className="text-lg text-gray-600 mb-8">
                        Your order{" "}
                        <span className="font-bold bg-green-100 px-3 py-1 rounded-full text-green-800">
                          #{orderId.substring(0, 8)}
                        </span>{" "}
                        has been placed successfully.
                      </p>

                      <div className="mt-8 space-y-6">
                        {/* Next Steps */}
                        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
                          <h3 className="font-semibold text-blue-900 mb-4">
                            What's Next?
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div className="flex items-start space-x-3">
                              <span className="text-2xl">📧</span>
                              <div>
                                <p className="font-medium text-blue-800">
                                  Confirmation Email
                                </p>
                                <p className="text-blue-700">
                                  Check your inbox for order details
                                </p>
                              </div>
                            </div>
                            <div className="flex items-start space-x-3">
                              <span className="text-2xl">📱</span>
                              <div>
                                <p className="font-medium text-blue-800">
                                  Track Your Order
                                </p>
                                <p className="text-blue-700">
                                  Monitor status in "My Orders"
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Method-specific instructions */}
                        {deliveryMethod === "pickup" && (
                          <div className="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-6">
                            <h3 className="font-semibold text-amber-900 mb-4">
                              📍 Pickup Instructions
                            </h3>
                            <div className="text-sm text-amber-800 space-y-2">
                              <p>
                                • <strong>Bring your receipt:</strong> Available
                                in your Order History
                              </p>
                              <p>
                                • <strong>Valid ID required</strong> when
                                collecting your order
                              </p>
                              <p>
                                • <strong>Pickup location</strong> details sent
                                via email
                              </p>
                              <p>
                                • <strong>Ready in 2-4 hours</strong> during
                                business hours
                              </p>
                            </div>
                          </div>
                        )}

                        {deliveryMethod === "delivery" &&
                          selectedProviderId && (
                            <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                              <h3 className="font-semibold text-green-900 mb-4">
                                🚚 Delivery Information
                              </h3>
                              <p className="text-sm text-green-800">
                                Your order will be delivered by{" "}
                                <strong>
                                  {
                                    deliveryProviders.find(
                                      (p) => p.id === selectedProviderId
                                    )?.name
                                  }
                                </strong>
                                {deliveryProviders.find(
                                  (p) => p.id === selectedProviderId
                                )?.estimated_delivery_time && (
                                  <span>
                                    {" "}
                                    within{" "}
                                    <strong>
                                      {
                                        deliveryProviders.find(
                                          (p) => p.id === selectedProviderId
                                        )?.estimated_delivery_time
                                      }
                                    </strong>
                                  </span>
                                )}
                              </p>
                            </div>
                          )}

                        <div className="flex flex-col sm:flex-row gap-4 justify-center pt-6">
                          <Link href="/my-orders" className="w-full sm:w-auto">
                            <Button
                              variant="outline"
                              className="w-full border-green-300 text-green-700 hover:bg-green-50"
                            >
                              📋 Track Order
                            </Button>
                          </Link>
                          <Link href="/products" className="w-full sm:w-auto">
                            <Button className="w-full bg-green-600 hover:bg-green-700">
                              🛍️ Continue Shopping
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Order Summary Sidebar - Hide for confirmation */}
          {currentStep !== "confirmation" && (
            <div className="lg:col-span-1">
              <Card className="sticky top-24">
                <CardHeader>
                  <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Cart Items Summary */}
                    <div className="space-y-2">
                      {cartItems.map((item) => (
                        <div
                          key={item.id}
                          className="flex justify-between text-sm"
                        >
                          <span className="truncate">
                            {item.product?.name} x{item.quantity}
                          </span>
                          <span>
                            {formatCurrency(
                              getEffectivePrice(item.product) * item.quantity,
                              currency
                            )}
                          </span>
                        </div>
                      ))}
                    </div>

                    <Separator />

                    {/* Totals */}
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Subtotal</span>
                        <span>{formatCurrency(subtotal, currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Service Fee</span>
                        <span>{formatCurrency(serviceFee, currency)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>
                          {deliveryMethod === "pickup"
                            ? "Store Pickup"
                            : deliveryMethod === "courier"
                            ? selectedProviderId && courierServices.length > 0
                              ? courierServices.find(
                                  (c) => c.id === selectedProviderId
                                )?.name || "Courier Service"
                              : "Courier Service"
                            : selectedProviderId && deliveryProviders.length > 0
                            ? deliveryProviders.find(
                                (p) => p.id === selectedProviderId
                              )?.name || "Delivery"
                            : "Delivery"}
                        </span>
                        <span>
                          {deliveryFee > 0
                            ? formatCurrency(deliveryFee, currency)
                            : (deliveryMethod === "delivery" ||
                                deliveryMethod === "courier") &&
                              !selectedProviderId
                            ? "Select provider"
                            : "Free"}
                        </span>
                      </div>
                    </div>

                    <Separator />

                    <div className="flex justify-between font-semibold">
                      <span>Total</span>
                      <span className="text-primary-600">
                        {formatCurrency(total, currency)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </EcommerceLayout>
  );
}
