import { NextRequest, NextResponse } from 'next/server';
import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';
import { DealsService } from '@/lib/services/deals';

export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    const supabase = await createServerAdminClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const { data: profile } = await supabase
      .from('profiles' as any)
      .select('role')
      .eq('id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Deactivate expired deals
    await DealsService.deactivateExpiredDeals();

    // Get count of deactivated deals for response
    const { data: expiredDeals, error } = await supabase
      .from('deals' as any)
      .select('id', { count: 'exact', head: true })
      .eq('is_active', false)
      .lt('end_date', new Date().toISOString());

    return NextResponse.json({
      success: true,
      message: 'Expired deals cleanup completed',
      deactivatedCount: expiredDeals?.length || 0
    });

  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Allow GET requests to check status
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerAdminClient();
    
    // Get expired but still active deals count
    const { count: expiredActiveCount } = await supabase
      .from('deals' as any)
      .select('id', { count: 'exact', head: true })
      .eq('is_active', true)
      .lt('end_date', new Date().toISOString());

    // Get total expired deals count
    const { count: totalExpiredCount } = await supabase
      .from('deals' as any)
      .select('id', { count: 'exact', head: true })
      .lt('end_date', new Date().toISOString());

    return NextResponse.json({
      expiredActiveDeals: expiredActiveCount || 0,
      totalExpiredDeals: totalExpiredCount || 0,
      needsCleanup: (expiredActiveCount || 0) > 0
    });

  } catch (error) {
    console.error('Error checking deals status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}