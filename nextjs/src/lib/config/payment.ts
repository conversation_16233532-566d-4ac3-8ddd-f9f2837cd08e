// Payment Configuration
// You can later move these to environment variables or database settings

export const PAYMENT_CONFIG = {
  wave: {
    number: "+220 123 4567", // Replace with your actual Wave number
    qrCodeUrl: null, // You can add QR code image URL here
    name: "Finder Marketplace", // Your business name
  },
  crypto: {
    bitcoin: {
      address: "**********************************", // Replace with your actual Bitcoin address
      qrCodeUrl: null, // You can add QR code image URL here
    },
    // You can add more crypto currencies here
    ethereum: {
      address: "******************************************", // Replace with your actual Ethereum address
      qrCodeUrl: null,
    }
  }
};

// Helper function to generate QR codes (you can implement this later)
export const generatePaymentQR = (paymentMethod: string, address: string, amount?: number) => {
  // For Wave: You might want to generate Wave QR format
  // For Bitcoin: bitcoin:address?amount=amount
  // For now, returns placeholder
  return null;
};

// Helper function to validate transaction IDs
export const validateTransactionId = (paymentMethod: string, transactionId: string): boolean => {
  if (!transactionId || transactionId.trim().length === 0) {
    return false;
  }

  switch (paymentMethod) {
    case 'wave':
      // Wave transaction IDs are typically numeric
      return /^[0-9]+$/.test(transactionId.trim());
    
    case 'crypto':
      // Bitcoin transaction hashes are 64 characters long, hexadecimal
      return /^[a-fA-F0-9]{64}$/.test(transactionId.trim());
    
    default:
      return transactionId.trim().length > 5; // Basic validation
  }
};