import { createSPASassClient } from '@/lib/supabase/client';

export interface ServiceFeeConfig {
  fee_type: 'fixed' | 'percentage' | 'tiered';
  fixed_fee?: number;
  percentage_fee?: number;
  tiers?: Array<{
    min_amount: number;
    max_amount: number | null;
    fee?: number;
    percentage?: number;
  }>;
  effective_date?: string | null;
  grace_period_days?: number;
}

export interface SystemSetting {
  id: string;
  setting_key: string;
  setting_value: any;
  description?: string;
  category: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export class SystemSettingsService {
  /**
   * Get a system setting by key
   */
  static async getSetting(key: string): Promise<any> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('system_settings')
        .select('setting_value')
        .eq('setting_key', key)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows found
          return null;
        }
        throw error;
      }
      
      return data?.setting_value || null;
    } catch (error) {
      console.error(`Error getting system setting ${key}:`, error);
      return null;
    }
  }

  /**
   * Update a system setting
   */
  static async updateSetting(key: string, value: any): Promise<boolean> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // First try to update existing setting
      const { data: updateData, error: updateError } = await supabase
        .from('system_settings')
        .update({
          setting_value: value,
          updated_at: new Date().toISOString()
        })
        .eq('setting_key', key);

      if (updateError) {
        // If update fails, try to insert new setting
        const { error: insertError } = await supabase
          .from('system_settings')
          .insert({
            setting_key: key,
            setting_value: value,
            category: 'fees',
            description: `Configuration for ${key}`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error(`Error inserting system setting ${key}:`, insertError);
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error(`Error updating system setting ${key}:`, error);
      return false;
    }
  }

  /**
   * Get all system settings
   */
  static async getAllSettings(): Promise<SystemSetting[]> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('system_settings')
        .select('*')
        .order('category', { ascending: true })
        .order('setting_key', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting all system settings:', error);
      return [];
    }
  }

  /**
   * Initialize default service fee configuration if none exists
   */
  static async initializeServiceFeeConfig(): Promise<boolean> {
    try {
      const existingConfig = await this.getSetting('service_fee_config');
      
      if (!existingConfig) {
        const defaultConfig: ServiceFeeConfig = {
          fee_type: 'tiered',
          tiers: [
            { min_amount: 0, max_amount: 1000, fee: 10.00 }, // 10 Dalasi for orders up to 1000 Dalasi
            { min_amount: 1001, max_amount: null, fee: 25.00 } // 25 Dalasi for orders above 1000 Dalasi
          ],
          grace_period_days: 7
        };
        
        return await this.updateSetting('service_fee_config', defaultConfig);
      }
      
      return true;
    } catch (error) {
      console.error('Error initializing service fee config:', error);
      return false;
    }
  }

  /**
   * Get service fee configuration
   */
  static async getServiceFeeConfig(): Promise<ServiceFeeConfig | null> {
    try {
      const config = await this.getSetting('service_fee_config');
      
      if (!config) {
        // Try to initialize default config
        const initialized = await this.initializeServiceFeeConfig();
        if (initialized) {
          return await this.getSetting('service_fee_config');
        }
        return null;
      }

      return config;
    } catch (error) {
      console.error('Error getting service fee config:', error);
      return null;
    }
  }

  /**
   * Update service fee configuration
   */
  static async updateServiceFeeConfig(config: ServiceFeeConfig): Promise<boolean> {
    return await this.updateSetting('service_fee_config', config);
  }

  /**
   * Calculate service fee based on current configuration
   */
  static async calculateServiceFee(amount: number): Promise<number> {
    try {
      const config = await this.getServiceFeeConfig();
      
      if (!config) {
        console.warn('No service fee configuration found, using fallback calculation');
        // Fallback calculation without hardcoded values - use minimal fee
        return amount > 1000 ? 25.00 : 10.00;
      }
      
      switch (config.fee_type) {
        case 'fixed':
          return config.fixed_fee || 0;
          
        case 'percentage':
          return (amount * (config.percentage_fee || 0)) / 100;
          
        case 'tiered':
          if (config.tiers) {
            for (const tier of config.tiers) {
              const meetsMin = amount >= tier.min_amount;
              const meetsMax = tier.max_amount === null || amount <= tier.max_amount;
              
              if (meetsMin && meetsMax) {
                if (tier.fee !== undefined) {
                  return tier.fee;
                } else if (tier.percentage !== undefined) {
                  return (amount * tier.percentage) / 100;
                }
              }
            }
          }
          // If no tier matches, return 0 instead of hardcoded fallback
          console.warn(`No matching tier found for amount ${amount}`);
          return 0;
          
        default:
          console.warn(`Unknown fee type: ${config.fee_type}`);
          return 0;
      }
    } catch (error) {
      console.error('Error calculating service fee:', error);
      // Return minimal fallback on error
      return amount > 1000 ? 25.00 : 10.00;
    }
  }

  /**
   * Check if a fee change is in grace period
   */
  static async isInGracePeriod(): Promise<boolean> {
    const config = await this.getServiceFeeConfig();
    
    if (!config || !config.effective_date || !config.grace_period_days) {
      return false;
    }

    const effectiveDate = new Date(config.effective_date);
    const gracePeriodEnd = new Date(effectiveDate);
    gracePeriodEnd.setDate(gracePeriodEnd.getDate() + config.grace_period_days);
    
    const now = new Date();
    return now <= gracePeriodEnd;
  }
}