import { createSPASassClient } from '@/lib/supabase/client';
import { Notification, CreateNotificationParams } from './notification';

export class NotificationClientService {
  // Get notifications for the current user
  static async getUserNotifications(limit = 10, offset = 0): Promise<{ notifications: Notification[], count: number }> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();
      
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      // Get count
      const { count, error: countError } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id);
      
      if (countError) throw countError;
      
      // Get notifications
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      if (error) throw error;
      
      return { 
        notifications: data as Notification[],
        count: count || 0
      };
    } catch (error) {
      console.error('Error getting user notifications:', error);
      return { notifications: [], count: 0 };
    }
  }

  // Get unread notification count for the current user
  static async getUnreadCount(): Promise<number> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();
      
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return 0;
      }
      
      // Get count of unread notifications
      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .eq('read', false);
      
      if (error) throw error;
      
      return count || 0;
    } catch (error) {
      console.error('Error getting unread notification count:', error);
      return 0;
    }
  }

  // Mark a notification as read
  static async markAsRead(notificationId: string): Promise<void> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();
      
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);
      
      if (error) throw error;
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  // Mark all notifications as read for the current user
  static async markAllAsRead(): Promise<void> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();
      
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', user.id)
        .eq('read', false);
      
      if (error) throw error;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }
}
