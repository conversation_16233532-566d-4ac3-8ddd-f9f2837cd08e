import { useState, useEffect } from 'react';
import { createSPASassClient } from '@/lib/supabase/client';
import { UserRole, UserWithRole, hasPermission, canAccessRoute } from '@/lib/types/roles';
import type { User } from '@supabase/supabase-js';

interface AuthState {
  user: User | null;
  profile: UserWithRole | null;
  role: UserRole;
  loading: boolean;
  error: string | null;
}

interface AuthActions {
  hasPermission: (permission: keyof import('@/lib/types/roles').RolePermissions) => boolean;
  canAccessRoute: (route: string) => boolean;
  isAdmin: boolean;
  isStoreOwner: boolean;
  isUser: boolean;
  refresh: () => Promise<void>;
}

export function useAuth(): AuthState & AuthActions {
  const [state, setState] = useState<AuthState>({
    user: null,
    profile: null,
    role: 'user',
    loading: true,
    error: null,
  });

  const loadUserData = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        throw userError;
      }

      if (!user) {
        setState({
          user: null,
          profile: null,
          role: 'user',
          loading: false,
          error: null,
        });
        return;
      }

      // Get user profile with role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('id, email, role, first_name, last_name, avatar_url')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.error('Error fetching profile:', profileError);
        // If profile doesn't exist, create one
        if (profileError.code === 'PGRST116') {
          const { data: newProfile, error: createError } = await supabase
            .from('profiles')
            .insert({
              id: user.id,
              email: user.email || '',
              role: 'user',
            })
            .select('id, email, role, first_name, last_name, avatar_url')
            .single();

          if (createError) {
            throw createError;
          }

          setState({
            user,
            profile: newProfile as UserWithRole,
            role: (newProfile?.role as UserRole) || 'user',
            loading: false,
            error: null,
          });
          return;
        }
        throw profileError;
      }

      setState({
        user,
        profile: profile as UserWithRole,
        role: (profile?.role as UserRole) || 'user',
        loading: false,
        error: null,
      });

    } catch (error) {
      console.error('Auth error:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Authentication error',
      }));
    }
  };

  useEffect(() => {
    loadUserData();

    // Set up auth state listener
    const setupAuthListener = async () => {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
            await loadUserData();
          } else if (event === 'SIGNED_OUT') {
            setState({
              user: null,
              profile: null,
              role: 'user',
              loading: false,
              error: null,
            });
          }
        }
      );

      return () => subscription.unsubscribe();
    };

    setupAuthListener();
  }, []);

  const refresh = async () => {
    await loadUserData();
  };

  return {
    ...state,
    hasPermission: (permission) => hasPermission(state.role, permission),
    canAccessRoute: (route) => canAccessRoute(state.role, route),
    isAdmin: state.role === 'admin',
    isUser: state.role === 'user',
    refresh,
  };
}

// Hook for checking specific role requirements
export function useRequireRole(requiredRole: UserRole | UserRole[]) {
  const auth = useAuth();

  const hasRequiredRole = Array.isArray(requiredRole)
    ? requiredRole.includes(auth.role)
    : auth.role === requiredRole;

  return {
    ...auth,
    hasRequiredRole,
    isAuthorized: !auth.loading && hasRequiredRole,
  };
}

// Hook for checking route access
export function useRouteAccess(route: string) {
  const auth = useAuth();

  return {
    ...auth,
    canAccess: auth.canAccessRoute(route),
    isAuthorized: !auth.loading && auth.canAccessRoute(route),
  };
}
