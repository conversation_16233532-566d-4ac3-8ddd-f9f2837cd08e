"use client";
import { <PERSON><PERSON><PERSON>, ChevronRight, ShieldCheck, Store } from 'lucide-react';
import Link from "next/link";
import { useAuth } from '@/lib/hooks/useAuth';

export default function AuthAwareButtons({ variant = 'primary' }) {
    const { user, role, loading, isAdmin, isStoreOwner } = useAuth();

    if (loading) {
        return null;
    }

    // Navigation buttons for the header
    if (variant === 'nav') {
        if (user) {
            return (
                <div className="flex items-center gap-3">
                    {(isAdmin || isStoreOwner) && (
                        <Link
                            href="/admin"
                            className="flex items-center gap-1 text-primary-600 hover:text-primary-700"
                        >
                            <ShieldCheck className="h-4 w-4" />
                            Admin
                        </Link>
                    )}
                    <Link
                        href="/app"
                        className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
                    >
                        Go to Dashboard
                    </Link>
                </div>
            );
        } else {
            return (
                <>
                    <Link href="/auth/login" className="text-gray-600 hover:text-gray-900">
                        Login
                    </Link>
                    <Link
                        href="/auth/register"
                        className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
                    >
                        Get Started
                    </Link>
                </>
            );
        }
    }

    // Primary buttons for the hero section
    if (user) {
        return (
            <div className="flex flex-col sm:flex-row gap-3">
                {(isAdmin || isStoreOwner) && (
                    <Link
                        href="/admin"
                        className="inline-flex items-center px-6 py-3 rounded-lg border border-primary-600 text-primary-600 font-medium hover:bg-primary-50 transition-colors"
                    >
                        Admin Dashboard
                        <ShieldCheck className="ml-2 h-5 w-5" />
                    </Link>
                )}
                <Link
                    href="/app"
                    className="inline-flex items-center px-6 py-3 rounded-lg bg-primary-600 text-white font-medium hover:bg-primary-700 transition-colors"
                >
                    Go to Dashboard
                    <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
            </div>
        );
    } else {
        return (
            <>
                <Link
                    href="/auth/register"
                    className="inline-flex items-center px-6 py-3 rounded-lg bg-primary-600 text-white font-medium hover:bg-primary-700 transition-colors"
                >
                    Start Building Free
                    <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
                <Link
                    href="#features"
                    className="inline-flex items-center px-6 py-3 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                >
                    Learn More
                    <ChevronRight className="ml-2 h-5 w-5" />
                </Link>
            </>
        );
    }
}