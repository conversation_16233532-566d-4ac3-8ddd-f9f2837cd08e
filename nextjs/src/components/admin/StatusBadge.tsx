import React from 'react';
import { Badge } from '@/components/ui/badge';

type StatusType =
  | 'active'
  | 'disabled'
  | 'suspended'
  | 'pending'
  | 'processing'
  | 'completed'
  | 'cancelled'
  | 'refunded'
  | 'paid'
  | 'unpaid'
  | 'failed'
  | 'success'
  | 'warning'
  | 'error'
  | 'info'
  | 'under_review'
  | 'awaiting_store_confirmation'
  | 'accepted_by_store'
  | 'shipped'
  | 'delivered';

interface StatusBadgeProps {
  status: string;
  size?: 'sm' | 'md' | 'lg';
}

export default function StatusBadge({ status, size = 'md' }: StatusBadgeProps) {
  const getVariant = (status: string) => {
    const statusLower = status.toLowerCase();

    switch (statusLower) {
      case 'active':
      case 'completed':
      case 'paid':
      case 'success':
      case 'delivered':
      case 'accepted_by_store':
        return 'success';
      case 'pending':
      case 'processing':
      case 'warning':
      case 'under_review':
      case 'awaiting_store_confirmation':
      case 'shipped':
        return 'warning';
      case 'disabled':
      case 'suspended':
      case 'cancelled':
      case 'refunded':
      case 'unpaid':
      case 'failed':
      case 'error':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return 'text-xs px-1.5 py-0.5';
      case 'lg':
        return 'text-sm px-3 py-1';
      default:
        return 'text-xs px-2 py-0.5';
    }
  };

  const getDisplayText = (status: string) => {
    const statusLower = status.toLowerCase();

    switch (statusLower) {
      case 'under_review':
        return 'Under Review';
      case 'awaiting_store_confirmation':
        return 'Awaiting Confirmation';
      case 'accepted_by_store':
        return 'Accepted by Store';
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  return (
    <Badge variant={getVariant(status) as any} className={getSizeClasses(size)}>
      {getDisplayText(status)}
    </Badge>
  );
}
