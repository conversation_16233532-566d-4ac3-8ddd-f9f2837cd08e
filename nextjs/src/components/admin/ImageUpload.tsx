'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import { Upload, X, Image as ImageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { createSPASassClient } from '@/lib/supabase/client';

interface ImageUploadProps {
  label: string;
  description?: string;
  currentImageUrl?: string | null;
  onImageUploaded: (url: string) => void;
  bucketName: string;
  folderPath: string;
  aspectRatio?: 'square' | 'cover' | 'logo';
  className?: string;
}

export function ImageUpload({
  label,
  description,
  currentImageUrl,
  onImageUploaded,
  bucketName,
  folderPath,
  aspectRatio = 'square',
  className = '',
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const aspectRatioClass = {
    square: 'aspect-square',
    cover: 'aspect-[16/9]',
    logo: 'aspect-square rounded-full',
  }[aspectRatio];

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please upload an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image size should be less than 5MB');
      return;
    }

    setError(null);
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Create a preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // Generate a unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `${folderPath}/${fileName}`;

      // Upload the file
      const client = await createSPASassClient();
      
      // Check if the bucket exists, create it if it doesn't
      try {
        const { data: buckets } = await client.getSupabaseClient().storage.listBuckets();
        const bucketExists = buckets?.some(bucket => bucket.name === bucketName);
        
        if (!bucketExists) {
          await client.getSupabaseClient().storage.createBucket(bucketName, {
            public: true
          });
        }
      } catch (error) {
        console.error('Error checking/creating bucket:', error);
      }

      const { data, error: uploadError } = await client.getSupabaseClient().storage
        .from(bucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true,
          onUploadProgress: (progress: { loaded: number; total: number; }) => {
            const percentage = (progress.loaded / progress.total) * 100;
            setUploadProgress(Math.round(percentage));
          }
        });

      if (uploadError) {
        throw uploadError;
      }

      // Get the public URL
      const { data: urlData } = client.getSupabaseClient().storage
        .from(bucketName)
        .getPublicUrl(filePath);

      // Call the callback with the URL
      onImageUploaded(urlData.publicUrl);

      // Clean up the object URL
      URL.revokeObjectURL(objectUrl);
      setPreviewUrl(urlData.publicUrl);
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveImage = () => {
    setPreviewUrl(null);
    onImageUploaded('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex flex-col space-y-1">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        {description && <p className="text-sm text-gray-500">{description}</p>}
      </div>

      {previewUrl ? (
        <div className="relative border rounded-md overflow-hidden">
          <div className={`relative ${aspectRatioClass} bg-gray-100`}>
            <Image
              src={previewUrl}
              alt={label}
              fill
              className={aspectRatio === 'logo' ? 'object-cover rounded-full' : 'object-cover'}
            />
          </div>
          <Button
            type="button"
            variant="destructive"
            size="sm"
            className="absolute top-2 right-2"
            onClick={handleRemoveImage}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      ) : (
        <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center">
          <ImageIcon className="h-12 w-12 text-gray-400" />
          <div className="mt-2 text-center">
            <p className="text-sm text-gray-500">
              {isUploading ? `Uploading... ${uploadProgress}%` : 'Click to upload or drag and drop'}
            </p>
            <p className="text-xs text-gray-400 mt-1">PNG, JPG, WebP up to 5MB</p>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            onChange={handleFileChange}
            accept="image/*"
            disabled={isUploading}
          />
        </div>
      )}

      {error && <p className="text-sm text-red-500">{error}</p>}
    </div>
  );
}
