'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useUserProfile } from '@/features/user/queries';
import { MapPin, User, Phone, Mail, Edit, AlertCircle, Plus } from 'lucide-react';


interface ShippingAddress {
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

interface AddressConfirmationProps {
  deliveryMethod: 'delivery' | 'pickup';
  onConfirm: (address?: ShippingAddress) => void;
  onEditAddress: () => void;
  onBack: () => void;
}

export default function AddressConfirmation({
  deliveryMethod,
  onConfirm,
  onEditAddress,
  onBack
}: AddressConfirmationProps) {
  const { data: profile, isLoading } = useUserProfile();
  const [selectedAddress, setSelectedAddress] = useState<ShippingAddress | null>(null);


  useEffect(() => {
    if (profile && hasCompleteAddress(profile)) {
      const profileAddress: ShippingAddress = {
        name: `${profile.first_name || ''} ${profile.last_name || ''}`.trim(),
        email: profile.email || '',
        phone: profile.phone || '',
        address: profile.address || '',
        city: profile.city || '',
        state: profile.state || '',
        country: profile.country || 'The Gambia',
        postalCode: profile.postal_code || '',
      };
      setSelectedAddress(profileAddress);
    }
  }, [profile]);

  const hasCompleteAddress = (profile: any) => {
    // Check if user has any address information filled out
    return profile && (
      profile.address || 
      profile.city || 
      profile.state || 
      profile.country || 
      profile.postal_code
    );
  };

  const handleConfirm = () => {
    if (deliveryMethod === 'pickup') {
      onConfirm(); // No address needed for pickup
    } else {
      onConfirm(selectedAddress || undefined);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // For pickup orders, just show confirmation
  if (deliveryMethod === 'pickup') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Pickup Confirmation
          </CardTitle>
          <CardDescription>
            You've selected to pick up your order from our location
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-start gap-3">
              <MapPin className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-1">Pickup Information</h4>
                <p className="text-sm text-blue-700">
                  • Pickup location: Our main store, Serrekunda, The Gambia<br />
                  • Available Monday - Saturday, 9 AM - 6 PM<br />
                  {/* • Pickup fee: {deliveryFee > 0 ? formatCurrency(deliveryFee, 'GMD') : 'Free'} */}
                </p>
              </div>
            </div>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You will receive a notification when your order is ready for pickup.
              Please bring a valid ID and your order confirmation.
            </AlertDescription>
          </Alert>

          <div className="flex gap-4">
            <Button type="button" variant="outline" onClick={onBack}>
              Back to Delivery Options
            </Button>
            <Button onClick={handleConfirm} className="flex-1">
              Continue to Payment
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // For delivery orders, show address confirmation
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Delivery Address Confirmation
        </CardTitle>
        <CardDescription>Please confirm your delivery address</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {selectedAddress ? (
          <>
            {/* Address Summary */}
            <div className="bg-gray-50 p-4 rounded-lg border">
              <div className="flex items-start justify-between mb-4">
                <h4 className="font-medium text-gray-900">Delivery Address</h4>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={onEditAddress}
                  className="flex items-center gap-2"
                >
                  <Edit className="h-4 w-4" />
                  Edit
                </Button>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">{selectedAddress.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    {selectedAddress.email}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    {selectedAddress.phone}
                  </span>
                </div>
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                  <div className="text-sm text-gray-600">
                    <div>{selectedAddress.address}</div>
                    <div>
                      {[
                        selectedAddress.city,
                        selectedAddress.state,
                        selectedAddress.postalCode,
                        selectedAddress.country,
                      ]
                        .filter(Boolean)
                        .join(", ")}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Delivery Information */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900 mb-1">
                    Delivery Information
                  </h4>
                  <p className="text-sm text-blue-700">
                    • Standard delivery within 2-3 business days
                    {/* <br />• Delivery fee: {formatCurrency(deliveryFee, "GMD")} */}
                    <br />• You will receive tracking information via SMS/email
                  </p>
                  {/* //{" "} */}
                  {/* <span className="text-base font-medium text-primary-600">
                    // {formatCurrency(deliveryFee, "GMD")}
                    //{" "}
                  </span> */}
                </div>
              </div>
            </div>

            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please ensure someone is available at the delivery address
                during business hours (9 AM - 6 PM).
              </AlertDescription>
            </Alert>

            <div className="flex gap-4">
              <Button type="button" variant="outline" onClick={onBack}>
                Back to Delivery Options
              </Button>
              <Button onClick={handleConfirm} className="flex-1">
                Confirm & Continue to Payment
              </Button>
            </div>
          </>
        ) : (
          <>
            {/* No Address Available */}
            <div className="text-center py-8">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Delivery Address Found
              </h3>
              <p className="text-gray-600 mb-6">
                You need to provide a delivery address to continue with delivery
                option.
              </p>

              <div className="space-y-3">
                <Button onClick={onEditAddress} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Delivery Address
                </Button>

                <Button
                  type="button"
                  variant="outline"
                  onClick={onBack}
                  className="w-full"
                >
                  Back to Delivery Options
                </Button>
              </div>
            </div>

            {!profile && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Complete your profile to save time on future orders.{" "}
                  <a
                    href="/account"
                    className="text-primary-600 hover:underline"
                  >
                    Update profile
                  </a>
                </AlertDescription>
              </Alert>
            )}

            {profile && !hasCompleteAddress(profile) && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Complete your address information in your profile to use it
                  for future orders.{" "}
                  <a
                    href="/account/addresses"
                    className="text-primary-600 hover:underline"
                  >
                    Manage addresses
                  </a>
                </AlertDescription>
              </Alert>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
